# 医学检查项目名称和编码处理流程

基于项目规则文件构建的标准化医学检查项目名称和编码处理Streamlit应用。

## 功能特性

### 🏥 完整的处理流程
- **数据加载与分析**：读取Excel源文件，进行基本统计分析和可视化
- **三级部位字典表生成**：清洗和验证部位数据，生成标准化字典表
- **扫描方式字典表生成**：处理CT和MR扫描方式，应用清理规则
- **检查项目清单生成**：根据规则生成标准化的检查项目名称和编码
- **数据分析与质量控制**：统计报告、错误分析、完整性验证

### 📊 交互式界面
- 分步骤处理，每个步骤可独立查看和操作
- 实时数据可视化（图表、统计信息）
- 分级查看和搜索功能
- 数据下载和Excel报告导出

### 🔧 标准化规则
- 项目名称格式：`[模态][部位]([扫描方式])`
- 项目编码格式：`[模态][部位编码][扫描编码]`（10位）
- 扫描方式清理：去除"CT-"、"MR-"等前缀
- 编码验证：六位部位编码、两位扫描编码

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行应用
```bash
streamlit run streamlit_medical_pipeline.py
```

### 3. 访问应用
在浏览器中打开 `http://localhost:8501`

## 数据文件要求

### Excel文件结构
应用需要包含以下工作表的Excel文件：

1. **三级部位结构**（主数据表）
   - 一级编码、一级部位、二级编码、二级合并
   - 三级编码、三级部位、部位编码
   - CT、MR适用标记
   - CT相关列：CT-平扫、CT-增强、CT-CTA等
   - MR相关列：MR-平扫、MR-增强、MR-MRA等

2. **CT扫描方式**
   - CT扫描分类编码、CT扫描分类名称
   - CT扫描编码、CT扫描名称

3. **MR扫描方式**
   - MR成像分类编码、MR成像分类
   - MR成像编码、MR成像名称

### 默认数据文件
应用会自动检测项目目录中的 `NEW_检查项目名称结构表 (8).xlsx` 文件。

## 使用流程

### 步骤1：数据加载与分析
- 上传Excel文件或使用默认文件
- 查看数据基本统计信息
- 分析部位分布和模态分布
- 预览各工作表数据

### 步骤2：三级部位字典表生成
- 自动清洗和验证部位数据
- 分级查看（一级、二级、三级部位）
- 搜索和筛选功能
- 数据质量检查（编码格式、重复性等）
- 下载CSV格式字典表

### 步骤3：扫描方式字典表生成
- 选择CT或MR模态
- 查看扫描方式清理前后对比
- 分析编码分布
- 下载扫描方式字典表

### 步骤4：检查项目清单生成
- 自动生成CT和MR检查项目
- 查看项目统计信息
- 项目格式示例展示
- 生成完整Excel报告

### 步骤5：数据分析与质量控制
- 综合统计报告
- 错误分析（编码格式、重复编码等）
- 数据完整性验证
- 部位覆盖分析

## 输出格式

### Excel报告包含工作表：
1. **CT检查项目清单** - 完整的CT项目列表
2. **MR检查项目清单** - 完整的MR项目列表
3. **字段字典** - 所有字段的定义和说明
4. **CT统计信息** - CT项目统计数据
5. **MR统计信息** - MR项目统计数据

### 数据列结构：
- 项目编码（10位）、项目名称、模态类型
- 一级部位、二级部位、三级部位、部位编码
- 扫描方式、扫描编码、创建时间

## 技术特性

### 数据处理
- 使用pandas进行高效数据处理
- 自动NaN值处理和数据类型转换
- 字符串清理和格式标准化

### 可视化
- Plotly交互式图表
- 实时数据统计和分布分析
- 响应式布局设计

### 质量控制
- 编码格式验证（10位项目编码）
- 重复性检查和完整性验证
- 错误报告和数据质量评估

## 项目结构

```
├── streamlit_medical_pipeline.py  # 主应用文件
├── requirements.txt               # 依赖包列表
├── README.md                     # 说明文档
├── NEW_检查项目名称结构表 (8).xlsx # 默认数据文件
└── .cursor/rules/                # 项目规则文件
    ├── 检查项目生成规则_V2.md
    ├── output-conventions.mdc
    ├── medical-naming-conventions.mdc
    └── excel-data-structure.mdc
```

## 注意事项

1. **数据源依赖**：确保Excel文件包含所有必要的工作表和列
2. **编码唯一性**：生成的编码在系统中应保持唯一性
3. **格式标准**：确保生成的名称符合医疗行业标准
4. **版本管理**：建议对生成结果进行版本控制
5. **数据更新**：当源数据更新时，需重新运行处理流程

## 支持的浏览器

- Chrome（推荐）
- Firefox
- Safari
- Edge

## 许可证

本项目遵循项目内部规则和约定，用于医学检查项目的标准化处理。
