#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析DR数据的表头，查看体位和方向列
"""

import pandas as pd

def analyze_dr_headers():
    """分析DR数据表头"""
    file_path = "data/DR项目结构-0706.xlsx"
    
    # 读取DR数据
    dr_data = pd.read_excel(file_path, sheet_name='DR')
    print(f"DR数据形状: {dr_data.shape}")
    
    print(f"\nDR数据所有列名:")
    for i, col in enumerate(dr_data.columns):
        print(f"{i+1:2d}. {col}")
    
    # 读取体位和方向数据
    try:
        position_data = pd.read_excel(file_path, sheet_name='体位')
        direction_data = pd.read_excel(file_path, sheet_name='方向')
        
        print(f"\n体位数据:")
        print(position_data.head(10))
        
        print(f"\n方向数据:")
        print(direction_data.head(10))
        
    except Exception as e:
        print(f"读取体位/方向数据时出错: {e}")
    
    # 分析DR数据中的体位和方向列
    position_columns = []
    direction_columns = []
    
    # 定义体位和方向的关键词
    position_keywords = ['仰卧位', '俯卧位', '左侧卧', '右侧卧', '斜位', '左前斜位', '右前斜位', 
                        '左后斜位', '右后斜位', '站立位', '倒立位', '内斜位', '外斜位', '外展位', 
                        '内收位', '过伸位', '过屈位', '张口位', '闭口位', '前弓位', '蛙形位', '全景', '应力位', '没有']
    
    direction_keywords = ['前后位', '后前位', '轴位', '前后轴位', '后前轴位', '切线位', 
                         '左前后斜位', '右前后斜位', '右后前斜位', '左后前斜位', '左侧位', 
                         '右侧位', '侧位', '穿胸位', '乳腺摄影', '头尾位', '内外斜位', 
                         '内外位', '外内位', '尾头位', '左弯曲位', '右弯曲位', '轴斜位']
    
    print(f"\n在DR表头中找到的体位相关列:")
    for col in dr_data.columns:
        if any(keyword in str(col) for keyword in position_keywords):
            position_columns.append(col)
            print(f"  - {col}")
    
    print(f"\n在DR表头中找到的方向相关列:")
    for col in dr_data.columns:
        if any(keyword in str(col) for keyword in direction_keywords):
            direction_columns.append(col)
            print(f"  - {col}")
    
    # 显示一些示例数据
    print(f"\n示例数据（前5行）:")
    sample_cols = ['三级部位', '项目名称', '摆位'] + position_columns[:5] + direction_columns[:5]
    available_cols = [col for col in sample_cols if col in dr_data.columns]
    print(dr_data[available_cols].head())
    
    return position_columns, direction_columns

if __name__ == "__main__":
    analyze_dr_headers()
