#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证改进的DR编码系统结果
"""

import pandas as pd
import json
import re

def validate_dr_results():
    """
    验证DR编码结果的正确性
    """
    # 读取JSON结果文件
    json_file = '/Users/<USER>/Desktop/12-new/改进的DR编码系统结果_20250706_025743.json'
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        df = pd.DataFrame(data)
        
        print("=== 改进的DR编码系统数据验证报告 ===")
        print(f"总项目数: {len(df)}")
        
        # 1. 编码格式验证
        print("\n1. 编码格式验证:")
        
        # 检查体位编码格式（应该是两位数字）
        valid_pos_format = df['体位编码'].str.match(r'^\d{2}$')
        print(f"   体位编码格式正确的项目: {valid_pos_format.sum()}/{len(df)}")
        if not valid_pos_format.all():
            invalid_pos = df[~valid_pos_format]['项目名称'].tolist()[:5]
            print(f"   体位编码格式错误的项目示例: {invalid_pos}")
        
        # 检查方向编码格式（应该是两位数字）
        valid_dir_format = df['方向编码'].str.match(r'^\d{2}$')
        print(f"   方向编码格式正确的项目: {valid_dir_format.sum()}/{len(df)}")
        if not valid_dir_format.all():
            invalid_dir = df[~valid_dir_format]['项目名称'].tolist()[:5]
            print(f"   方向编码格式错误的项目示例: {invalid_dir}")
        
        # 检查摆位编码格式（应该是XX_XX格式）
        valid_pose_format = df['摆位编码'].str.match(r'^\d{2}_\d{2}$')
        print(f"   摆位编码格式正确的项目: {valid_pose_format.sum()}/{len(df)}")
        
        # 2. 摆位编码统计
        print("\n2. 摆位编码分布（前10种）:")
        pose_counts = df['摆位编码'].value_counts().head(10)
        for pose_code, count in pose_counts.items():
            print(f"   {pose_code}: {count}个项目")
        
        # 3. 体位分布
        print("\n3. 体位分布:")
        pos_counts = df['体位名称'].value_counts()
        for pos_name, count in pos_counts.items():
            pos_code = df[df['体位名称'] == pos_name]['体位编码'].iloc[0]
            print(f"   {pos_name}({pos_code}): {count}个项目")
        
        # 4. 方向分布
        print("\n4. 方向分布:")
        dir_counts = df['方向名称'].value_counts()
        for dir_name, count in dir_counts.items():
            dir_code = df[df['方向名称'] == dir_name]['方向编码'].iloc[0]
            print(f"   {dir_name}({dir_code}): {count}个项目")
        
        # 5. 部位编码完整性
        print("\n5. 部位编码完整性:")
        has_level1 = df['一级部位'].notna() & (df['一级部位'] != '')
        has_level2 = df['二级部位'].notna() & (df['二级部位'] != '')
        has_level3 = df['三级部位'].notna() & (df['三级部位'] != '')
        has_level3_code = df['三级部位编码'].notna()
        
        print(f"   有一级部位的项目: {has_level1.sum()}/{len(df)}")
        print(f"   有二级部位的项目: {has_level2.sum()}/{len(df)}")
        print(f"   有三级部位的项目: {has_level3.sum()}/{len(df)}")
        print(f"   有三级部位编码的项目: {has_level3_code.sum()}/{len(df)}")
        
        # 6. 缺失部位信息的项目
        missing_structure = df[~(has_level1 & has_level2 & has_level3)]
        if not missing_structure.empty:
            print(f"\n6. 缺失部位信息的项目 ({len(missing_structure)}个):")
            for i, row in missing_structure.head(10).iterrows():
                print(f"   {row['项目名称']} - 部位: {row['一级部位']}>{row['二级部位']}>{row['三级部位']}")
        
        # 7. 特殊编码检查
        print("\n7. 特殊编码使用情况:")
        no_position = df[df['体位编码'] == '99']
        no_direction = df[df['方向编码'] == '99']
        print(f"   使用'没有'体位编码(99)的项目: {len(no_position)}个")
        print(f"   使用'没有'方向编码(99)的项目: {len(no_direction)}个")
        
        if len(no_position) > 0:
            print("   '没有'体位编码的项目示例:")
            for name in no_position['项目名称'].head(5):
                print(f"     - {name}")
        
        if len(no_direction) > 0:
            print("   '没有'方向编码的项目示例:")
            for name in no_direction['项目名称'].head(5):
                print(f"     - {name}")
        
        # 8. 数据一致性检查
        print("\n8. 数据一致性检查:")
        
        # 检查摆位编码是否与体位、方向编码一致
        consistent_pose = df.apply(lambda row: row['摆位编码'] == f"{row['体位编码']}_{row['方向编码']}", axis=1)
        print(f"   摆位编码一致性: {consistent_pose.sum()}/{len(df)}")
        
        if not consistent_pose.all():
            inconsistent = df[~consistent_pose]
            print("   摆位编码不一致的项目:")
            for i, row in inconsistent.head(3).iterrows():
                print(f"     {row['项目名称']}: 期望{row['体位编码']}_{row['方向编码']}, 实际{row['摆位编码']}")
        
        # 9. 总体评估
        print("\n9. 总体评估:")
        
        format_score = (valid_pos_format.sum() + valid_dir_format.sum() + valid_pose_format.sum()) / (3 * len(df)) * 100
        structure_score = has_level3_code.sum() / len(df) * 100
        consistency_score = consistent_pose.sum() / len(df) * 100
        
        print(f"   编码格式正确率: {format_score:.1f}%")
        print(f"   部位编码完整率: {structure_score:.1f}%")
        print(f"   数据一致性: {consistency_score:.1f}%")
        
        overall_score = (format_score + structure_score + consistency_score) / 3
        print(f"   总体质量评分: {overall_score:.1f}%")
        
        # 10. 建议
        print("\n10. 改进建议:")
        if structure_score < 100:
            print("   - 需要补充部位编码信息，特别是从NEW_检查项目名称结构表中获取正确的编码")
        if format_score < 100:
            print("   - 需要修正编码格式错误")
        if len(no_position) > 0 or len(no_direction) > 0:
            print("   - 建议为使用'没有'编码的项目补充具体的体位或方向信息")
        
        print("\n=== 验证完成 ===")
        
        return {
            'total_items': len(df),
            'format_score': format_score,
            'structure_score': structure_score,
            'consistency_score': consistency_score,
            'overall_score': overall_score
        }
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        return None

if __name__ == '__main__':
    validate_dr_results()