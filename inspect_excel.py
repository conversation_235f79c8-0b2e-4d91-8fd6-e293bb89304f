import pandas as pd

# 读取Excel文件
file_path = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0706.xlsx'

try:
    # 读取所有sheet
    excel_data = pd.read_excel(file_path, sheet_name=None)
    
    print("=== Excel文件结构检查 ===")
    print(f"可用的sheet: {list(excel_data.keys())}")
    
    # 检查DR sheet
    if 'DR' in excel_data:
        dr_sheet = excel_data['DR']
        print(f"\n=== DR Sheet ===")
        print(f"形状: {dr_sheet.shape}")
        print(f"列名: {dr_sheet.columns.tolist()}")
        print(f"\n前5行数据:")
        print(dr_sheet.head())
        
        if '三级部位' in dr_sheet.columns:
            print(f"\n三级部位列的数据类型: {dr_sheet['三级部位'].dtype}")
            print(f"三级部位列的唯一值数量: {dr_sheet['三级部位'].nunique()}")
            print(f"三级部位列的前10个值: {dr_sheet['三级部位'].head(10).tolist()}")
    
    # 检查三级部位 sheet
    if '三级部位' in excel_data:
        level3_sheet = excel_data['三级部位']
        print(f"\n=== 三级部位 Sheet ===")
        print(f"形状: {level3_sheet.shape}")
        print(f"列名: {level3_sheet.columns.tolist()}")
        print(f"\n前5行数据:")
        print(level3_sheet.head())
        
        if '三级部位' in level3_sheet.columns:
            print(f"\n三级部位列的数据类型: {level3_sheet['三级部位'].dtype}")
            print(f"三级部位列的唯一值数量: {level3_sheet['三级部位'].nunique()}")
            print(f"三级部位列的前10个值: {level3_sheet['三级部位'].head(10).tolist()}")
            print(f"三级部位列的所有唯一值:")
            print(level3_sheet['三级部位'].unique())
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()