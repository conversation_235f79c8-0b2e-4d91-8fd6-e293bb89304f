#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的DR编码系统
功能：
1. 从NEW_检查项目名称结构表中获取正确的DR检查项目部位编码
2. 将摆位编码改为两位数字编码
3. 生成包含所有正确信息的最终表格
"""

import pandas as pd
import json
import numpy as np
from datetime import datetime
import os

def read_structure_table(file_path):
    """
    读取NEW_检查项目名称结构表
    """
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取主要的结构表（通常是第一个工作表）
        structure_df = pd.read_excel(file_path, sheet_name=0)
        print(f"结构表形状: {structure_df.shape}")
        print(f"结构表列名: {list(structure_df.columns)}")
        
        return structure_df
    except Exception as e:
        print(f"读取结构表时出错: {e}")
        return None

def create_two_digit_position_encoding():
    """
    创建两位数摆位编码系统
    体位编码：01-99
    方向编码：01-99
    """
    # 体位编码映射
    position_mapping = {
        '仰卧位': '01',
        '俯卧位': '02', 
        '侧卧位': '03',
        '立位': '04',
        '坐位': '05',
        '斜位': '06',
        '没有': '99'
    }
    
    # 方向编码映射
    direction_mapping = {
        '后前位': '01',
        '前后位': '02',
        '侧位': '03',
        '斜位': '04',
        '轴位': '05',
        '切线位': '06',
        '没有': '99'
    }
    
    return position_mapping, direction_mapping

def analyze_dr_structure_and_generate_improved_encoding():
    """
    分析DR结构并生成改进的编码
    """
    # 读取原始DR项目结构文件
    dr_file_path = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx'
    
    try:
        # 读取DR项目数据
        dr_df = pd.read_excel(dr_file_path, sheet_name='DR')
        print(f"DR项目数据形状: {dr_df.shape}")
        print(f"DR项目列名: {list(dr_df.columns)}")
        
        # 读取体位和方向编码表
        position_df = pd.read_excel(dr_file_path, sheet_name='体位')
        direction_df = pd.read_excel(dr_file_path, sheet_name='方向')
        
        print(f"体位编码表形状: {position_df.shape}")
        print(f"方向编码表形状: {direction_df.shape}")
        
    except Exception as e:
        print(f"读取DR项目文件时出错: {e}")
        return None
    
    # 读取结构表
    structure_file_path = '/Users/<USER>/Desktop/12-new/data/NEW_检查项目名称结构表 (8).xlsx'
    structure_df = read_structure_table(structure_file_path)
    
    if structure_df is None:
        print("无法读取结构表，使用默认部位编码")
        structure_df = pd.DataFrame()
    
    # 创建两位数编码系统
    position_mapping, direction_mapping = create_two_digit_position_encoding()
    
    # 分析结果列表
    results = []
    
    # 处理每个DR项目
    for idx, row in dr_df.iterrows():
        project_name = row.get('项目名称', '')
        if pd.isna(project_name) or project_name == '':
            continue
            
        # 查找匹配的结构信息
        structure_info = find_matching_structure(project_name, structure_df)
        
        # 分析体位和方向
        position_info, direction_info = analyze_position_and_direction(row, position_df, direction_df)
        
        # 生成两位数编码
        position_code = position_mapping.get(position_info['name'], '99')
        direction_code = direction_mapping.get(direction_info['name'], '99')
        
        # 生成摆位编码
        pose_code = f"{position_code}_{direction_code}"
        
        result = {
            '项目名称': project_name,
            '一级部位': structure_info.get('一级部位', ''),
            '一级部位编码': structure_info.get('一级部位编码', ''),
            '二级部位': structure_info.get('二级部位', ''),
            '二级部位编码': structure_info.get('二级部位编码', ''),
            '三级部位': structure_info.get('三级部位', ''),
            '三级部位编码': structure_info.get('三级部位编码', ''),
            '体位名称': position_info['name'],
            '体位编码': position_code,
            '方向名称': direction_info['name'],
            '方向编码': direction_code,
            '摆位编码': pose_code,
            '激活的体位': position_info['activated'],
            '激活的方向': direction_info['activated']
        }
        
        results.append(result)
    
    return results

def find_matching_structure(project_name, structure_df):
    """
    查找匹配的结构信息
    """
    if structure_df.empty:
        return {
            '一级部位': '',
            '一级部位编码': '',
            '二级部位': '',
            '二级部位编码': '',
            '三级部位': '',
            '三级部位编码': ''
        }
    
    # 尝试精确匹配
    if '项目名称' in structure_df.columns:
        exact_match = structure_df[structure_df['项目名称'] == project_name]
        if not exact_match.empty:
            row = exact_match.iloc[0]
            return {
                '一级部位': row.get('一级部位', ''),
                '一级部位编码': row.get('一级部位编码', ''),
                '二级部位': row.get('二级部位', ''),
                '二级部位编码': row.get('二级部位编码', ''),
                '三级部位': row.get('三级部位', ''),
                '三级部位编码': row.get('三级部位编码', '')
            }
    
    # 尝试模糊匹配（基于项目名称的关键词）
    keywords = ['头颅', '胸部', '腹部', '骨盆', '脊柱', '四肢', '颈椎', '胸椎', '腰椎']
    for keyword in keywords:
        if keyword in project_name:
            keyword_match = structure_df[structure_df.apply(lambda x: keyword in str(x).lower(), axis=1)]
            if not keyword_match.empty:
                row = keyword_match.iloc[0]
                return {
                    '一级部位': row.get('一级部位', ''),
                    '一级部位编码': row.get('一级部位编码', ''),
                    '二级部位': row.get('二级部位', ''),
                    '二级部位编码': row.get('二级部位编码', ''),
                    '三级部位': row.get('三级部位', ''),
                    '三级部位编码': row.get('三级部位编码', '')
                }
    
    # 默认返回空值
    return {
        '一级部位': '',
        '一级部位编码': '',
        '二级部位': '',
        '二级部位编码': '',
        '三级部位': '',
        '三级部位编码': ''
    }

def analyze_position_and_direction(row, position_df, direction_df):
    """
    分析体位和方向信息
    """
    # 分析体位
    position_info = {'name': '没有', 'activated': []}
    for _, pos_row in position_df.iterrows():
        pos_name = pos_row.get('体位', '')
        if pd.notna(pos_name) and pos_name in row:
            if pd.notna(row[pos_name]) and row[pos_name] == 1:
                position_info['name'] = pos_name
                position_info['activated'].append(pos_name)
    
    # 分析方向
    direction_info = {'name': '没有', 'activated': []}
    for _, dir_row in direction_df.iterrows():
        dir_name = dir_row.get('方向', '')
        if pd.notna(dir_name) and dir_name in row:
            if pd.notna(row[dir_name]) and row[dir_name] == 1:
                direction_info['name'] = dir_name
                direction_info['activated'].append(dir_name)
    
    return position_info, direction_info

def save_results_and_generate_report(results):
    """
    保存结果并生成报告
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存为Excel文件
    excel_filename = f'改进的DR编码系统结果_{timestamp}.xlsx'
    excel_path = f'/Users/<USER>/Desktop/12-new/output/{excel_filename}'
    
    # 确保输出目录存在
    os.makedirs('/Users/<USER>/Desktop/12-new/output', exist_ok=True)
    
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False, engine='openpyxl')
    
    # 保存为JSON文件
    json_filename = f'改进的DR编码系统结果_{timestamp}.json'
    json_path = f'/Users/<USER>/Desktop/12-new/{json_filename}'
    
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 生成统计报告
    print("\n=== 改进的DR编码系统分析报告 ===")
    print(f"处理项目总数: {len(results)}")
    
    # 统计编码分布
    position_codes = [r['体位编码'] for r in results]
    direction_codes = [r['方向编码'] for r in results]
    
    print(f"\n体位编码分布:")
    position_dist = pd.Series(position_codes).value_counts()
    for code, count in position_dist.items():
        pos_name = next((r['体位名称'] for r in results if r['体位编码'] == code), '未知')
        print(f"  {code} ({pos_name}): {count}个项目")
    
    print(f"\n方向编码分布:")
    direction_dist = pd.Series(direction_codes).value_counts()
    for code, count in direction_dist.items():
        dir_name = next((r['方向名称'] for r in results if r['方向编码'] == code), '未知')
        print(f"  {code} ({dir_name}): {count}个项目")
    
    # 检查缺失部位编码的项目
    missing_structure = [r for r in results if not r['一级部位编码']]
    if missing_structure:
        print(f"\n缺失部位编码的项目数量: {len(missing_structure)}")
        print("前5个缺失部位编码的项目:")
        for i, item in enumerate(missing_structure[:5]):
            print(f"  {i+1}. {item['项目名称']}")
    
    print(f"\n结果已保存到:")
    print(f"  Excel文件: {excel_path}")
    print(f"  JSON文件: {json_path}")
    
    return excel_path, json_path

def main():
    """
    主函数
    """
    print("开始执行改进的DR编码系统...")
    
    # 分析DR结构并生成改进编码
    results = analyze_dr_structure_and_generate_improved_encoding()
    
    if results:
        # 保存结果并生成报告
        excel_path, json_path = save_results_and_generate_report(results)
        
        # 验证数据正确性
        print("\n=== 数据验证 ===")
        
        # 验证编码格式
        valid_codes = True
        for result in results:
            pos_code = result['体位编码']
            dir_code = result['方向编码']
            
            if not (pos_code.isdigit() and len(pos_code) == 2):
                print(f"体位编码格式错误: {result['项目名称']} - {pos_code}")
                valid_codes = False
            
            if not (dir_code.isdigit() and len(dir_code) == 2):
                print(f"方向编码格式错误: {result['项目名称']} - {dir_code}")
                valid_codes = False
        
        if valid_codes:
            print("✓ 所有编码格式正确（两位数字）")
        
        # 验证摆位编码唯一性
        pose_codes = [r['摆位编码'] for r in results]
        unique_poses = len(set(pose_codes))
        print(f"摆位编码种类数: {unique_poses}")
        
        # 显示前10个结果作为样例
        print("\n=== 前10个结果样例 ===")
        for i, result in enumerate(results[:10]):
            print(f"{i+1}. {result['项目名称']}")
            print(f"   部位: {result['一级部位']} > {result['二级部位']} > {result['三级部位']}")
            print(f"   摆位: {result['体位名称']}({result['体位编码']}) + {result['方向名称']}({result['方向编码']}) = {result['摆位编码']}")
            print()
        
        print("改进的DR编码系统执行完成！")
        return excel_path
    else:
        print("执行失败，无法生成结果")
        return None

if __name__ == '__main__':
    main()