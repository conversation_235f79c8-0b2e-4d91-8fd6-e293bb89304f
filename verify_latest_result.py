#!/usr/bin/env python3
import pandas as pd
import os
from datetime import datetime

# 找到最新的结果文件
output_dir = '/Users/<USER>/Desktop/12-new/output'
result_files = [f for f in os.listdir(output_dir) if f.startswith('DR三级部位匹配结果_') and f.endswith('.xlsx')]

if result_files:
    # 按时间戳排序，获取最新文件
    latest_file = sorted(result_files)[-1]
    file_path = os.path.join(output_dir, latest_file)
    
    print(f"验证文件: {latest_file}")
    print(f"完整路径: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        print(f"\n文件基本信息:")
        print(f"总行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查匹配情况
        # 有编码信息的行被认为是匹配成功的
        matched_rows = df[df[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].notna().any(axis=1)]
        unmatched_rows = df[df[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].isna().all(axis=1)]
        
        print(f"\n匹配统计:")
        print(f"匹配成功: {len(matched_rows)} 行")
        print(f"未匹配: {len(unmatched_rows)} 行")
        print(f"空行: {len(df) - len(matched_rows) - len(unmatched_rows)} 行")
        
        # 显示匹配成功的示例
        if len(matched_rows) > 0:
            print(f"\n匹配成功的数据示例 (前5行):")
            print(matched_rows[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位']].head())
        
        # 显示未匹配的示例
        if len(unmatched_rows) > 0:
            print(f"\n未匹配的数据示例 (前10行):")
            unmatched_parts = unmatched_rows['三级部位'].dropna().head(10).tolist()
            print(unmatched_parts)
        
        # 检查数据质量
        print(f"\n数据质量检查:")
        print(f"三级部位列空值数量: {df['三级部位'].isna().sum()}")
        print(f"三级部位列唯一值数量: {df['三级部位'].nunique()}")
        
        # 检查是否有重复的三级部位
        duplicated_parts = df[df['三级部位'].duplicated(keep=False)]['三级部位'].value_counts()
        if len(duplicated_parts) > 0:
            print(f"\n重复的三级部位 (前10个):")
            print(duplicated_parts.head(10))
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        import traceback
        traceback.print_exc()
else:
    print("未找到结果文件")