import pandas as pd

# 读取生成的结果文件
result_file = 'output/DR部位匹配结果_20250706_034241.xlsx'

try:
    df = pd.read_excel(result_file, sheet_name='匹配结果')
    print(f"结果文件行数: {len(df)}")
    print(f"结果文件列数: {len(df.columns)}")
    print(f"\n列名:")
    for i, col in enumerate(df.columns):
        print(f"{i+1:2d}. {col}")

    print(f"\n前10行数据:")
    print(df.head(10))

    print(f"\n匹配状态统计:")
    if '匹配状态' in df.columns:
        print(df['匹配状态'].value_counts())

    print(f"\n未匹配的记录示例:")
    unmatched = df[df['匹配状态'] == '未匹配'] if '匹配状态' in df.columns else pd.DataFrame()
    if len(unmatched) > 0:
        print(unmatched[['三级部位', '项目名称', '一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].head(10))

    # 检查是否有未匹配的sheet
    try:
        unmatched_df = pd.read_excel(result_file, sheet_name='未匹配记录')
        print(f"\n未匹配记录sheet行数: {len(unmatched_df)}")
        print("未匹配记录前5行:")
        print(unmatched_df[['三级部位', '项目名称']].head())
    except:
        print("\n没有找到未匹配记录sheet")

except Exception as e:
    print(f"读取文件时出错: {e}")
    import traceback
    traceback.print_exc()