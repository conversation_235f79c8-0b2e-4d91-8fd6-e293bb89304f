#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_detailed_result():
    """验证详细结果的正确性"""

    # 读取结果文件
    result_file = 'output/DR完整匹配结果_20250706_050139.xlsx'
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')

    print("=== 详细结果验证 ===")
    print(f"总行数: {len(df)}")
    print(f"总列数: {len(df.columns)}")

    # 检查列顺序
    expected_columns = [
        '一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '部位编码',
        '项目名称', 'DR检查项目编码', '摆位', '摆位编码', '体位', '体位编码', '方向', '方向编码'
    ]

    print(f"\n列顺序检查:")
    for i, expected_col in enumerate(expected_columns):
        actual_col = df.columns[i] if i < len(df.columns) else "缺失"
        status = "✓" if actual_col == expected_col else "✗"
        print(f"{i+1:2d}. {status} 期望: {expected_col:<12} 实际: {actual_col}")

    # 显示前几行数据
    print(f"\n前5行数据预览:")
    sample_cols = ['一级编码', '一级部位', '三级部位', '项目名称', 'DR检查项目编码', '摆位编码', '体位', '方向']
    print(df[sample_cols].head())

    # 检查有项目的记录
    has_projects = df[df['项目名称'] != '']
    print(f"\n有DR项目的记录: {len(has_projects)}")

    # 检查摆位编码的完整性
    print(f"\n摆位编码分析:")
    position_codes = has_projects['摆位编码']
    print(f"摆位编码长度分布: {position_codes.str.len().value_counts().to_dict()}")
    print(f"摆位编码示例: {position_codes.head().tolist()}")

    # 检查体位和方向的匹配
    print(f"\n体位方向匹配分析:")
    position_body = has_projects['体位'].value_counts().head(10)
    print(f"最常见的体位:")
    print(position_body)

    direction_counts = has_projects['方向'].value_counts().head(10)
    print(f"\n最常见的方向:")
    print(direction_counts)

    # 检查编码的一致性
    print(f"\n编码一致性检查:")
    sample_records = has_projects.head(10)

    for idx, row in sample_records.iterrows():
        expected_position_code = str(row['体位编码']) + str(row['方向编码'])
        actual_position_code = row['摆位编码']
        status = "✓" if expected_position_code == actual_position_code else "✗"

        print(f"{status} 体位:{row['体位'][:8]:<8} 方向:{row['方向'][:8]:<8} "
              f"体位码:{row['体位编码']} 方向码:{row['方向编码']} "
              f"期望:{expected_position_code} 实际:{actual_position_code}")

    # 检查DR检查项目编码的格式
    print(f"\nDR检查项目编码格式检查:")
    dr_codes = has_projects['DR检查项目编码']

    for idx, row in sample_records.iterrows():
        expected_dr_code = f"DR{row['部位编码']}{row['摆位编码']}"
        actual_dr_code = row['DR检查项目编码']
        status = "✓" if expected_dr_code == actual_dr_code else "✗"

        print(f"{status} 部位码:{row['部位编码']} 摆位码:{row['摆位编码']} "
              f"期望:{expected_dr_code} 实际:{actual_dr_code}")

    # 统计无项目的部位
    no_projects = df[df['项目名称'] == '']
    print(f"\n无DR项目的部位: {len(no_projects)}")
    if len(no_projects) > 0:
        print("无项目的部位列表:")
        for idx, row in no_projects.iterrows():
            print(f"  - {row['一级部位']} > {row['二级部位']} > {row['三级部位']}")

if __name__ == "__main__":
    verify_detailed_result()
