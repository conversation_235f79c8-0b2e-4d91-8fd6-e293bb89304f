#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_position_codes():
    """验证摆位编码的正确性"""

    # 读取结果文件
    result_file = 'output/DR完整匹配结果_20250706_044506.xlsx'
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')

    # 读取原始数据
    file_path = "data/DR项目结构-0706.xlsx"
    position_data = pd.read_excel(file_path, sheet_name='体位')
    direction_data = pd.read_excel(file_path, sheet_name='方向')

    print("=== 摆位编码验证 ===")

    # 显示体位编码映射
    print("\n体位编码映射:")
    for idx, row in position_data.head(15).iterrows():
        if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
            print(f"  {row.iloc[1]} -> {row.iloc[0]}")

    # 显示方向编码映射
    print("\n方向编码映射:")
    for idx, row in direction_data.head(15).iterrows():
        if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
            print(f"  {row.iloc[1]} -> {row.iloc[0]}")

    # 分析DR检查项目编码
    dr_codes = df[df['DR检查项目编码'] != '']['DR检查项目编码']

    print(f"\nDR检查项目编码分析:")
    print(f"总编码数: {len(dr_codes)}")

    # 提取摆位编码部分（最后2位）
    position_codes = dr_codes.str[-2:]
    print(f"摆位编码统计:")
    print(position_codes.value_counts().head(10))

    # 显示一些具体的编码示例
    print(f"\n编码示例分析:")
    sample_records = df[df['DR检查项目编码'] != ''].head(10)

    for idx, row in sample_records.iterrows():
        dr_code = row['DR检查项目编码']
        position_part = dr_code[-2:]  # 最后2位是摆位编码
        part_code = dr_code[2:8]      # 中间6位是部位编码

        print(f"项目: {row['项目名称'][:15]:<15} | 摆位: {row['摆位'][:10]:<10} | "
              f"完整编码: {dr_code} | 部位: {part_code} | 摆位: {position_part}")

    # 检查编码格式
    print(f"\n编码格式检查:")
    all_codes = df[df['DR检查项目编码'] != '']['DR检查项目编码']

    # 检查长度
    lengths = all_codes.str.len()
    print(f"编码长度分布: {lengths.value_counts().to_dict()}")

    # 检查是否都以DR开头
    starts_with_dr = all_codes.str.startswith('DR')
    print(f"以DR开头的编码数: {starts_with_dr.sum()}/{len(all_codes)}")

    # 检查部位编码部分（第3-8位）
    part_codes = all_codes.str[2:8]
    print(f"部位编码长度检查: {part_codes.str.len().value_counts().to_dict()}")

    # 检查摆位编码部分（第9-10位）
    pos_codes = all_codes.str[8:10]
    print(f"摆位编码长度检查: {pos_codes.str.len().value_counts().to_dict()}")

    # 显示一些特殊的摆位编码
    print(f"\n摆位编码分布:")
    pos_code_counts = pos_codes.value_counts()
    print(pos_code_counts.head(15))

if __name__ == "__main__":
    verify_position_codes()
