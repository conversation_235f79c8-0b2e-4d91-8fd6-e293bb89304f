#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取修正后的结果文件
result_file = 'output/DR部位匹配结果_修正版_20250706_041741.xlsx'

try:
    # 读取匹配结果sheet
    df = pd.read_excel(result_file, sheet_name='匹配结果')
    print(f"修正后匹配结果:")
    print(f"总行数: {len(df)}")
    print(f"列数: {len(df.columns)}")

    print(f"\n匹配状态统计:")
    print(df['匹配状态'].value_counts())

    # 检查列的排列顺序
    print(f"\n列的排列顺序:")
    for i, col in enumerate(df.columns[:12]):  # 显示前12列
        print(f"{i+1:2d}. {col}")

    # 显示前几行已匹配的数据
    print(f"\n已匹配数据示例:")
    matched = df[df['匹配状态'] == '已匹配']
    print(matched[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目名称', '摆位']].head(10))

    # 显示未匹配的数据
    print(f"\n未匹配数据:")
    unmatched = df[df['匹配状态'] == '未匹配']
    if len(unmatched) > 0:
        print(unmatched[['三级部位_原始_DR', '三级部位', '项目名称']].head(15))

    # 读取统计信息
    stats_df = pd.read_excel(result_file, sheet_name='统计信息')
    print(f"\n统计信息:")
    print(stats_df)

    # 检查股骨髁间窝是否匹配成功
    gugu_records = df[df['三级部位_原始_DR'].str.contains('股骨髁间窝', na=False)]
    print(f"\n股骨髁间窝匹配效果:")
    if len(gugu_records) > 0:
        print(gugu_records[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目名称', '三级部位_原始_DR', '匹配状态']].head())
    else:
        print("未找到股骨髁间窝相关记录")

    # 检查眼眶是否匹配成功
    eye_records = df[df['三级部位_原始_DR'].str.contains('眼', na=False)]
    print(f"\n眼眶相关记录:")
    print(eye_records[['一级编码', '一级部位', '二级编码', '二级部位', '三级部位', '项目名称', '匹配状态']].head())

    # 检查侧字符处理效果
    ce_records = df[df['三级部位_原始_DR'].str.contains('侧', na=False)]
    print(f"\n侧字符处理效果:")
    print(ce_records[['一级编码', '一级部位', '三级部位_原始_DR', '三级部位', '项目名称', '匹配状态']].head(5))

except Exception as e:
    print(f"读取文件时出错: {e}")
    import traceback
    traceback.print_exc()
