#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版DR项目结构数据匹配处理脚本
1. 去除DR数据中的"侧"字符
2. 修正眼眶/眼框问题
3. 清理前后空格
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def clean_part_name(part_name):
    """清理部位名称"""
    if pd.isna(part_name):
        return part_name

    # 转换为字符串并去除前后空格
    cleaned = str(part_name).strip()

    # 去除"侧"字符
    cleaned = cleaned.replace('侧', '')

    # 特殊处理：眼眶/眼框统一
    if cleaned == '眼框':
        cleaned = '眼眶'

    # 特殊处理：股骨髁间窝匹配到股骨
    if '股骨髁间窝' in cleaned:
        cleaned = cleaned.replace('股骨髁间窝', '股骨')

    return cleaned

def load_and_clean_data(file_path):
    """加载并清理数据"""
    try:
        # 读取DR sheet
        dr_data = pd.read_excel(file_path, sheet_name='DR')
        print(f"DR sheet 原始数据形状: {dr_data.shape}")

        # 读取三级部位 sheet
        part_data = pd.read_excel(file_path, sheet_name='三级部位')
        print(f"三级部位 sheet 原始数据形状: {part_data.shape}")

        # 清理DR数据
        dr_clean = dr_data.dropna(subset=['三级部位']).copy()
        dr_clean['三级部位_原始'] = dr_clean['三级部位'].copy()  # 保留原始值
        dr_clean['三级部位'] = dr_clean['三级部位'].apply(clean_part_name)

        # 清理三级部位数据
        part_clean = part_data.dropna(subset=['三级部位']).copy()
        part_clean['三级部位_原始'] = part_clean['三级部位'].copy()  # 保留原始值
        part_clean['三级部位'] = part_clean['三级部位'].apply(clean_part_name)

        print(f"清理后DR数据行数: {len(dr_clean)}")
        print(f"清理后三级部位数据行数: {len(part_clean)}")

        # 显示清理效果
        print(f"\nDR数据清理示例:")
        changes = dr_clean[dr_clean['三级部位'] != dr_clean['三级部位_原始']]
        for idx, row in changes.head(10).iterrows():
            print(f"  '{row['三级部位_原始']}' -> '{row['三级部位']}'")

        print(f"\n三级部位数据清理示例:")
        changes = part_clean[part_clean['三级部位'] != part_clean['三级部位_原始']]
        for idx, row in changes.head(10).iterrows():
            print(f"  '{row['三级部位_原始']}' -> '{row['三级部位']}'")

        return dr_clean, part_clean
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None, None

def match_and_merge_data(dr_data, part_data):
    """匹配DR数据和三级部位数据并合并"""

    print(f"\n=== 开始匹配 ===")

    # 显示唯一的三级部位值
    dr_unique = sorted(dr_data['三级部位'].unique())
    part_unique = sorted(part_data['三级部位'].unique())

    print(f"DR数据中的唯一三级部位数量: {len(dr_unique)}")
    print(f"三级部位数据中的唯一三级部位数量: {len(part_unique)}")

    # 找出交集和差集
    dr_set = set(dr_unique)
    part_set = set(part_unique)

    intersection = dr_set & part_set
    dr_only = dr_set - part_set
    part_only = part_set - dr_set

    print(f"\n匹配统计:")
    print(f"可以匹配的三级部位: {len(intersection)}")
    print(f"DR独有的三级部位: {len(dr_only)}")
    print(f"三级部位表独有的三级部位: {len(part_only)}")

    if dr_only:
        print(f"\nDR独有的三级部位:")
        for part in sorted(dr_only):
            print(f"  - {part}")

    # 执行左连接匹配
    merged_data = pd.merge(
        dr_data,
        part_data,
        on='三级部位',
        how='left',
        indicator=True,
        suffixes=('_DR', '_部位')
    )

    # 统计匹配结果
    match_stats = merged_data['_merge'].value_counts()
    print(f"\n详细匹配统计:")
    print(match_stats)

    # 找出未匹配的记录
    unmatched = merged_data[merged_data['_merge'] == 'left_only'].copy()
    if len(unmatched) > 0:
        print(f"\n未匹配的记录数: {len(unmatched)}")
        print("未匹配的三级部位:")
        unmatched_parts = unmatched['三级部位'].unique()
        for part in sorted(unmatched_parts):
            if pd.notna(part):
                print(f"  - {part}")

    # 删除merge指示列
    merged_data = merged_data.drop('_merge', axis=1)

    return merged_data, unmatched

def create_final_output(merged_data, unmatched_data):
    """创建最终输出格式"""

    # 创建最终数据框
    final_data = merged_data.copy()

    # 添加匹配状态列
    final_data['匹配状态'] = '已匹配'
    if len(unmatched_data) > 0:
        unmatched_indices = unmatched_data.index
        final_data.loc[final_data.index.isin(unmatched_indices), '匹配状态'] = '未匹配'

    # 重新排列列顺序：一级编码、一级部位、二级编码、二级部位、三级编码、三级部位、项目名称、摆位
    desired_columns = [
        '一级编码',          # 从三级部位表匹配来的
        '一级部位',          # 从三级部位表匹配来的
        '二级编码',          # 从三级部位表匹配来的
        '二级部位',          # 从三级部位表匹配来的
        '三级编码',          # 从三级部位表匹配来的
        '三级部位',          # 清理后的三级部位（匹配用）
        '项目名称',          # DR中的项目名称
        '摆位',             # DR中的摆位信息
        '三级部位_原始_DR',  # DR中的原始三级部位
        '部位编码',          # 从三级部位表匹配来的
        'DR',               # 从三级部位表匹配来的
        '匹配状态'
    ]

    # 重新排列列顺序，只保留存在的列
    available_columns = [col for col in desired_columns if col in final_data.columns]
    other_columns = [col for col in final_data.columns if col not in desired_columns and col not in ['三级部位_原始_DR', '三级部位_原始_部位']]

    # 最终列顺序
    final_column_order = available_columns + other_columns
    final_data = final_data[final_column_order]

    # 在未匹配行后插入空行
    if len(unmatched_data) > 0:
        result_list = []
        for idx, row in final_data.iterrows():
            result_list.append(row)
            if row['匹配状态'] == '未匹配':
                # 插入空行
                empty_row = pd.Series([np.nan] * len(final_data.columns), index=final_data.columns)
                empty_row['匹配状态'] = '--- 空行分隔 ---'
                result_list.append(empty_row)

        final_data = pd.DataFrame(result_list).reset_index(drop=True)

    return final_data

def main():
    """主函数"""
    file_path = "data/DR项目结构-0706.xlsx"

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    print("开始处理DR项目结构数据（修正版）...")

    # 加载和清理数据
    dr_data, part_data = load_and_clean_data(file_path)
    if dr_data is None or part_data is None:
        return

    # 匹配和合并数据
    merged_data, unmatched_data = match_and_merge_data(dr_data, part_data)
    if merged_data is None:
        return

    # 创建最终输出
    final_data = create_final_output(merged_data, unmatched_data)

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output/DR部位匹配结果_修正版_{timestamp}.xlsx"

    os.makedirs("output", exist_ok=True)

    # 统计信息
    matched_count = len(final_data[final_data['匹配状态'] == '已匹配'])
    unmatched_count = len(final_data[final_data['匹配状态'] == '未匹配'])
    total_count = matched_count + unmatched_count

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主结果sheet
        final_data.to_excel(writer, sheet_name='匹配结果', index=False)

        # 仅未匹配记录sheet
        if len(unmatched_data) > 0:
            unmatched_summary = unmatched_data[['三级部位_原始_DR', '三级部位', '项目名称']].copy()
            unmatched_summary.to_excel(writer, sheet_name='未匹配记录', index=False)

        # 统计信息sheet
        stats_data = {
            '统计项目': ['已匹配记录数', '未匹配记录数', '总记录数', '匹配率'],
            '数值': [matched_count, unmatched_count, total_count, f"{matched_count/total_count*100:.1f}%"]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)

    print(f"\n处理完成！结果已保存到: {output_file}")
    print(f"总记录数: {total_count}")
    print(f"已匹配记录数: {matched_count}")
    print(f"未匹配记录数: {unmatched_count}")
    print(f"匹配率: {matched_count/total_count*100:.1f}%")

if __name__ == "__main__":
    main()
