# Python生成器编码规范

本规则适用于所有检查项目生成器Python文件。

## 编码标准

### 1. 文件头部
- 使用UTF-8编码：`# -*- coding: utf-8 -*-`
- 包含详细的模块文档字符串
- 说明生成规则和要求

### 2. 类设计原则
- 使用面向对象设计
- 主类名：`CheckItemGenerator` 或 `CheckItemGeneratorV2`
- 方法命名使用下划线分隔
- 每个方法都有详细的文档字符串

### 3. 数据处理规范
- 使用pandas处理Excel数据
- 所有NaN值检查：`pd.notna(value)`
- 字符串清理：去除空格、特殊字符
- 数据类型转换：`str(value).strip()`

### 4. 编码生成规则
- 扫描编码：两位数字，使用`zfill(2)`
- 部位编码：六位数字
- 最终编码：无空格，格式验证

### 5. 异常处理
- 文件加载异常处理
- 数据缺失异常处理
- 编码格式验证

### 6. 输出格式
- 使用`pd.ExcelWriter`创建多工作表Excel文件
- 包含数据清单、字段字典、统计信息
- 文件命名包含时间戳

## 关键方法

### 必需方法
- `load_data()` - 加载Excel数据
- `create_scan_mapping()` - 创建扫描方式映射
- `clean_scan_name()` - 清理扫描方式名称
- `find_scan_code()` - 查找扫描编码
- `generate_ct_items()` - 生成CT项目
- `generate_mr_items()` - 生成MR项目
- `export_results()` - 导出结果

### 数据验证
- 检查数据完整性
- 验证编码格式
- 确保映射准确性

## 示例代码模式

```python
def clean_scan_name(self, scan_name, modality):
    """清理扫描方式名称"""
    if pd.isna(scan_name):
        return ""
    
    scan_name = str(scan_name).strip()
    
    if modality == 'CT':
        scan_name = scan_name.replace('CT-', '').replace('CT_', '')
    elif modality == 'MR':
        scan_name = scan_name.replace('MR-', '').replace('MR_', '')
    
    return re.sub(r'\s+', ' ', scan_name).strip()
```
