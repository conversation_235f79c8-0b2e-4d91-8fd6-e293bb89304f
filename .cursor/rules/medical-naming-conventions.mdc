# 医学检查项目命名和编码约定

本规则定义了医学影像检查项目的标准命名和编码约定。

## 项目名称格式

### 基本格式
```
格式: "[模态][部位]([扫描方式])"
```

### 命名规范
- **模态前缀**: CT, MR (大写)
- **部位名称**: 使用标准医学术语，中文
- **扫描方式**: 括号内，简化描述

### 示例
```
CT颅脑(平扫)
CT颅脑(增强)
CT颅脑(平扫+增强)
MR颅脑(平扫)
MR颅脑(MRA)
MR颅脑(水成像MRCP)
```

## 项目编码格式

### 基本格式
```
格式: "[模态][部位编码][扫描编码]"
总长度: 10位字符
```

### 编码结构
- **模态代码**: CT, MR (2位字符)
- **部位编码**: 6位数字 (一级+二级+三级编码)
- **扫描编码**: 2位数字

### 部位编码规则
```
部位编码 = 一级编码(2位) + 二级编码(2位) + 三级编码(2位)
示例: 010101 = 01(头部) + 01(颅脑) + 01(颅脑)
```

### 扫描编码规则
- **CT扫描编码**:
  - 10: 平扫
  - 20: 增强
  - 30: CTA
  - 31: CTV
  - 40: 灌注
- **MR扫描编码**:
  - 10: 平扫
  - 20: 增强
  - 30: MRA
  - 31: MRV

### 完整编码示例
```
CT01010110 = CT + 010101 + 10 (CT颅脑平扫)
MR01010120 = MR + 010101 + 20 (MR颅脑增强)
```

## 数据清理规则

### 扫描方式清理
- 去除前缀: 移除"CT-", "MR-", "CT_", "MR_"
- 去除多余空格
- 保持简洁描述

### 部位名称清理
- 保持标准医学术语
- 去除多余空格
- 保持中文原文

## 质量验证

### 必须检查
- 编码长度正确 (10位)
- 编码格式正确 (模态+部位+扫描)
- 名称格式正确 (模态部位(扫描))
- 无重复编码
- 无空值或无效数据

### 数据完整性
- 所有部位都有对应编码
- 所有扫描方式都有对应编码
- 映射关系正确

## 医学术语标准

### 常用部位术语
- 头部: 颅脑、颅底、鞍区、头颅
- 颈部: 颈椎、甲状腺、颈部血管
- 胸部: 胸部、肺、心脏、纵隔
- 腹部: 腹部、肝脏、胆囊、胰腺
- 盆部: 盆腔、膀胱、前列腺、子宫
- 脊柱: 颈椎、胸椎、腰椎、骶椎
- 四肢及关节: 肩关节、肘关节、腕关节、髋关节

### 常用扫描方式
- CT: 平扫、增强、CTA、CTV、灌注、延时显像
- MR: 平扫、增强、MRA、MRV、水成像、电影成像、弥散成像
