# 输出文件格式和约定

本规则定义了检查项目生成器的输出文件格式和命名约定。

## 输出文件命名

### 基本格式
```
[项目名称]_[生成时间].xlsx
示例: 检查项目清单_20241214_143022.xlsx
```

### 命名规范
- 使用中文描述性名称
- 包含生成时间戳 (YYYYMMDD_HHMMSS)
- 使用xlsx格式
- 避免特殊字符和空格

## Excel工作表结构

### 必需工作表
1. **CT检查项目清单** - CT项目完整列表
2. **MR检查项目清单** - MR项目完整列表  
3. **字段字典** - 所有字段的定义和说明
4. **CT统计信息** - CT项目统计数据
5. **MR统计信息** - MR项目统计数据

### 工作表命名规范
- 使用中文描述性名称
- 名称简洁明了
- 保持一致性

## 数据列结构

### 检查项目清单工作表
必需列 (按顺序):
1. `项目编码` (object): 10位项目唯一编码
2. `项目名称` (object): 标准格式项目名称
3. `模态类型` (object): CT或MR
4. `一级部位` (object): 一级部位名称
5. `二级部位` (object): 二级部位名称
6. `三级部位` (object): 三级部位名称
7. `部位编码` (object): 6位部位编码
8. `扫描方式` (object): 扫描方式名称
9. `扫描编码` (object): 2位扫描编码
10. `创建时间` (object): 生成时间戳

### 字段字典工作表
必需列:
1. `字段名称` (object): 字段的标准名称
2. `字段类型` (object): 数据类型
3. `字段描述` (object): 详细说明
4. `示例值` (object): 典型示例
5. `数据格式` (object): 格式要求
6. `是否必填` (object): 是/否
7. `备注` (object): 补充说明

### 统计信息工作表
必需列:
1. `统计项目` (object): 统计项名称
2. `统计值` (object): 统计结果
3. `统计时间` (object): 统计时间戳

## 数据质量标准

### 数据完整性
- 所有必需列不能为空
- 编码格式必须符合标准
- 名称格式必须一致
- 时间戳必须正确

### 数据准确性
- 编码不能重复
- 名称与编码对应正确
- 统计数据准确
- 映射关系正确

### 数据格式
- 编码使用固定位数
- 名称使用标准格式
- 时间使用标准格式
- 数字使用正确精度

## 文件输出规范

### 文件保存
- 使用相对路径保存
- 自动创建输出目录
- 覆盖同名文件前提醒
- 保存成功后显示路径

### 文件编码
- 使用UTF-8编码
- 支持中文字符
- 兼容Excel显示
- 确保跨平台兼容

### 文件大小
- 优化数据结构减少文件大小
- 清理无用数据
- 压缩重复内容
- 控制在合理范围内

## 错误处理

### 文件保存错误
- 检查文件是否被占用
- 验证写入权限
- 处理路径错误
- 提供错误提示

### 数据导出错误
- 检查数据完整性
- 验证数据格式
- 处理空值异常
- 记录错误日志

## 成功输出示例

### 控制台输出
```
生成完成！
==================
CT项目数量: 333
MR项目数量: 555
总项目数量: 888
==================
输出文件: 检查项目清单_20241214_143022.xlsx
包含工作表:
- CT检查项目清单
- MR检查项目清单  
- 字段字典
- CT统计信息
- MR统计信息
==================
```

### 文件结构验证
- 文件可正常打开
- 所有工作表存在
- 数据格式正确
- 中文显示正常
- 公式计算正确

## 版本控制

### 文件版本
- 通过时间戳区分版本
- 保留历史版本
- 清理过期文件
- 版本比较功能

### 数据版本
- 记录源数据版本
- 跟踪生成参数
- 比较版本差异
- 恢复历史版本

## 使用建议

### 文件管理
- 定期清理旧文件
- 备份重要版本
- 分类存储文件
- 建立命名规范

### 数据验证
- 生成后验证数据
- 检查统计信息
- 确认映射关系
- 测试实际应用
