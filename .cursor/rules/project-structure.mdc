# 检查项目生成系统项目结构

这是一个医学影像检查项目名称和编码生成系统，用于标准化CT和MR检查项目。

## 核心文件结构

- [generate_check_items_v2.py](mdc:generate_check_items_v2.py) - 主要的检查项目生成器V2.0
- [generate_check_items.py](mdc:generate_check_items.py) - 原始版本的生成器
- [检查项目生成规则_V2.md](mdc:检查项目生成规则_V2.md) - 详细的生成规则和要求文档
- [检查项目生成方案说明.md](mdc:检查项目生成方案说明.md) - 原始方案说明

## 数据源文件

- Excel文件包含多个工作表：
  - `三级部位结构` - 主数据表，包含部位层级和模态配置
  - `CT扫描方式` - CT扫描方式编码对照表
  - `MR扫描方式` - MR扫描方式编码对照表
  - `方向` - 方向信息
  - `体位` - 体位信息

## 生成规则

### 项目名称格式
```
格式: "[模态][部位]([扫描方式])"
示例: CT颅脑(平扫), MR颅脑(增强)
```

### 项目编码格式
```
格式: "[模态][部位编码][扫描编码]"
长度: 10位字符
示例: CT01010110 (CT + 010101 + 10)
```

## 输出结果

生成的Excel文件包含：
- CT检查项目清单
- MR检查项目清单
- 字段字典
- 统计信息

使用 `python3 generate_check_items_v2.py` 运行最新版本的生成器。