# 医学检查项目处理系统 - 使用指南

## 📋 项目概述

本系统是一个完整的医学检查项目名称和编码处理流程，支持从Excel数据源自动生成标准化的CT和MR检查项目清单，包括有部位项目和无部位项目。

## 🎯 主要功能

### 1. 检查项目生成
- **有部位项目**：基于三级部位结构和扫描方式生成
- **无部位项目**：基于扫描方式字典表生成独立项目
- **标准编码**：10位标准项目编码格式
- **标准命名**：规范化的项目名称格式

### 2. 数据输出
- **标准9列格式**：模态、一级编码、一级部位、二级编码、二级部位、三级编码、三级部位、项目编码、项目名称
- **Excel导出**：包含多个工作表的完整报告
- **Web界面**：交互式数据查看和筛选

### 3. 质量控制
- **数据验证**：编码格式、数据完整性检查
- **重复检测**：项目编码唯一性验证
- **统计分析**：项目数量、分布统计

## 📁 文件结构

```
cleaned_project/
├── streamlit_simple.py              # 主要的Streamlit Web应用
├── demo_script.py                   # 命令行演示脚本
├── NEW_检查项目名称结构表 (8).xlsx    # 原始数据文件
├── requirements.txt                 # Python依赖包
├── README.md                       # 项目说明
├── test_column_format.py           # 列格式测试脚本
├── test_no_part_items.py           # 无部位项目测试脚本
├── Excel格式修改验证报告.md         # Excel格式功能验证报告
├── 无部位项目功能验证报告.md         # 无部位项目功能验证报告
├── 医学检查项目处理结果_*.xlsx      # 示例输出文件
└── 使用指南.md                     # 本文件
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. Web界面使用
```bash
# 启动Web应用
streamlit run streamlit_simple.py

# 访问地址：http://localhost:8501
```

### 3. 命令行使用
```bash
# 运行完整处理流程
python demo_script.py
```

## 🌐 Web界面操作指南

### 步骤1：数据加载
- 系统自动加载原始Excel文件
- 显示数据概览和统计信息

### 步骤2：三级部位字典生成
- 自动生成三级部位编码字典
- 显示部位层级结构

### 步骤3：扫描方式字典生成
- 生成CT和MR扫描方式字典
- 建立扫描方式编码映射

### 步骤4：检查项目清单生成 ⭐
- **项目统计**：显示CT/MR项目总数，有部位/无部位项目分类
- **筛选查看**：
  - 全部项目：查看完整项目清单
  - 有部位项目：查看基于部位结构的项目
  - 无部位项目：查看基于扫描方式的独立项目
- **Excel导出**：生成包含所有项目的完整报告

### 步骤5：数据分析与质量控制
- 综合统计报告
- 编码错误分析
- 扫描方式匹配率统计

## 📊 输出格式说明

### 标准9列格式
| 列序号 | 列名称 | 说明 | 示例 |
|--------|--------|------|------|
| 1 | 模态 | CT或MR | CT |
| 2 | 一级编码 | 一级部位编码 | 01 |
| 3 | 一级部位 | 一级部位名称 | 头部 |
| 4 | 二级编码 | 二级部位编码 | 01 |
| 5 | 二级部位 | 二级部位名称 | 颅脑 |
| 6 | 三级编码 | 三级部位编码 | 01 |
| 7 | 三级部位 | 三级部位名称 | 颅脑 |
| 8 | 项目编码 | 10位项目编码 | CT01010110 |
| 9 | 项目名称 | 标准格式项目名称 | CT颅脑(平扫) |

### 项目编码规则

#### 有部位项目编码
- **格式**：[模态][一级编码][二级编码][三级编码][扫描编码]
- **示例**：CT01010110 = CT + 01(头部) + 01(颅脑) + 01(颅脑) + 10(平扫)

#### 无部位项目编码
- **格式**：[模态]09[扫描分类编码][扫描编码]00
- **示例**：CT09011000 = CT + 09(无指定部位) + 01(CT平扫) + 10(CT平扫) + 00

## 🔧 测试和验证

### 列格式测试
```bash
python test_column_format.py
```
验证输出的列顺序是否符合标准9列格式。

### 无部位项目测试
```bash
python test_no_part_items.py
```
验证无部位项目生成规则和数据完整性。

## 📈 项目统计示例

```
项目生成统计：
CT项目总数：386 (有部位: 329, 无部位: 57)
MR项目总数：609 (有部位: 554, 无部位: 55)
总项目数：995
无部位项目占比：11.2%
```

## 🎯 核心特性

### 1. 无部位项目处理 ✨
- **CT无部位项目**：基于CT扫描方式字典表生成
- **MR无部位项目**：基于MR扫描方式字典表生成
- **独立编码**：使用"09"作为一级编码，避免与有部位项目冲突
- **完整集成**：无缝合并到主项目清单中

### 2. 标准化输出
- **统一格式**：所有项目使用相同的9列标准格式
- **编码规范**：10位标准项目编码
- **命名规范**：标准化的项目名称格式

### 3. 质量保证
- **数据验证**：自动检查数据完整性和格式正确性
- **重复检测**：确保项目编码唯一性
- **错误报告**：详细的质量控制分析

## 🔍 常见问题

### Q: 如何区分有部位项目和无部位项目？
A: 通过一级部位字段区分：
- 有部位项目：一级部位为具体部位名称（如"头部"、"胸部"等）
- 无部位项目：一级部位为"无指定部位"

### Q: 无部位项目的作用是什么？
A: 无部位项目代表通用的扫描方式，不依赖于特定的身体部位，适用于：
- 通用扫描技术
- 特殊成像方法
- 技术加收项目

### Q: 如何自定义数据源？
A: 替换`NEW_检查项目名称结构表 (8).xlsx`文件，确保包含以下工作表：
- 三级部位结构
- CT扫描方式
- MR扫描方式

## 📞 技术支持

如有问题，请参考：
1. `Excel格式修改验证报告.md` - Excel输出格式详细说明
2. `无部位项目功能验证报告.md` - 无部位项目功能详细说明
3. 运行测试脚本进行功能验证

---

**版本**：v2.0  
**更新时间**：2025-07-05  
**主要功能**：标准9列格式输出 + 无部位项目处理
