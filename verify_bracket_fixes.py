#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def verify_bracket_fixes():
    """验证括号修正和重复内容处理效果"""
    
    # 读取结果文件
    result_file = 'output/DR完整匹配结果_20250706_051345.xlsx'
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')
    
    print("=== 括号修正和重复内容处理验证 ===")
    
    # 获取有项目的记录
    has_projects = df[df['项目名称'] != '']
    print(f"有项目名称的记录数: {len(has_projects)}")
    
    print(f"\n1. 中文括号改为英文括号检查:")
    
    # 检查是否还有中文括号
    chinese_brackets_count = 0
    english_brackets_count = 0
    
    for idx, row in has_projects.iterrows():
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        
        if '（' in project_name or '）' in project_name:
            chinese_brackets_count += 1
        if '(' in project_name or ')' in project_name:
            english_brackets_count += 1
    
    print(f"包含中文括号的项目名称: {chinese_brackets_count}")
    print(f"包含英文括号的项目名称: {english_brackets_count}")
    
    # 显示包含括号的项目名称示例
    bracket_examples = []
    for idx, row in has_projects.iterrows():
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        if '(' in project_name and ')' in project_name:
            bracket_examples.append(project_name)
    
    print(f"\n包含英文括号的项目名称示例:")
    for example in bracket_examples[:15]:
        print(f"  - {example}")
    
    print(f"\n2. 重复括号内容处理检查:")
    
    # 查找可能的重复括号内容
    duplicate_bracket_cases = []
    
    for idx, row in has_projects.iterrows():
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        part_name = str(row['三级部位']) if pd.notna(row['三级部位']) else ''
        position = str(row['摆位']) if pd.notna(row['摆位']) else ''
        
        # 检查项目名称中是否有重复的括号内容
        if '-' in project_name:
            part_in_name = project_name.split('-')[0][2:]  # 去掉"DR"
            position_in_name = project_name.split('-', 1)[1]
            
            # 提取括号内容
            part_brackets = re.findall(r'\([^)]*\)', part_in_name)
            position_brackets = re.findall(r'\([^)]*\)', position_in_name)
            
            # 检查是否有重复
            for bracket in part_brackets:
                if bracket in position_brackets:
                    duplicate_bracket_cases.append({
                        '项目名称': project_name,
                        '部位': part_name,
                        '摆位': position,
                        '重复括号': bracket
                    })
    
    if duplicate_bracket_cases:
        print(f"发现 {len(duplicate_bracket_cases)} 个重复括号内容的情况:")
        for case in duplicate_bracket_cases[:10]:
            print(f"  - {case['项目名称']} (重复: {case['重复括号']})")
    else:
        print("✓ 未发现重复括号内容")
    
    print(f"\n3. 特定案例检查:")
    print("检查您提到的左胫腓骨案例:")
    
    # 查找左胫腓骨相关记录
    tibia_cases = []
    for idx, row in has_projects.iterrows():
        part_name = str(row['三级部位']) if pd.notna(row['三级部位']) else ''
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        
        if '左胫腓骨' in part_name:
            tibia_cases.append({
                '三级部位': part_name,
                '项目名称': project_name,
                '部位编码': row['部位编码'],
                '摆位': row['摆位'],
                'DR检查项目编码': row['DR检查项目编码']
            })
    
    print(f"找到 {len(tibia_cases)} 个左胫腓骨相关记录:")
    for case in tibia_cases:
        print(f"  部位: {case['三级部位']}")
        print(f"  项目: {case['项目名称']}")
        print(f"  编码: {case['DR检查项目编码']}")
        print(f"  摆位: {case['摆位']}")
        print()
    
    print(f"\n4. 括号处理前后对比:")
    
    # 显示一些典型的括号处理效果
    bracket_comparison = []
    for idx, row in has_projects.iterrows():
        original_position = str(row['摆位']) if pd.notna(row['摆位']) else ''
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        part_name = str(row['三级部位']) if pd.notna(row['三级部位']) else ''
        
        # 查找包含括号的情况
        if ('（' in original_position or '）' in original_position or 
            '(' in original_position or ')' in original_position):
            
            if '-' in project_name:
                cleaned_position = project_name.split('-', 1)[1]
                bracket_comparison.append({
                    '部位': part_name,
                    '原始摆位': original_position,
                    '清理后摆位': cleaned_position,
                    '项目名称': project_name
                })
    
    print("括号处理示例:")
    for comp in bracket_comparison[:15]:
        print(f"  部位: {comp['部位']}")
        print(f"  原始: {comp['原始摆位']}")
        print(f"  清理: {comp['清理后摆位']}")
        print(f"  项目: {comp['项目名称']}")
        print()

if __name__ == "__main__":
    verify_bracket_fixes()
