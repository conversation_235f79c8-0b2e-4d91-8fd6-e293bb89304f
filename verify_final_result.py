#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取最终结果文件
result_file = 'output/DR三级部位匹配最终结果_20250706_034419.xlsx'

try:
    # 读取匹配结果sheet
    df = pd.read_excel(result_file, sheet_name='匹配结果')
    print(f"匹配结果sheet:")
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    print(f"\n列名: {list(df.columns)}")
    
    print(f"\n前10行数据:")
    print(df.head(10))
    
    print(f"\n匹配状态统计:")
    print(df['匹配状态'].value_counts())
    
    # 读取未匹配记录sheet
    unmatched_df = pd.read_excel(result_file, sheet_name='未匹配记录')
    print(f"\n未匹配记录sheet:")
    print(f"行数: {len(unmatched_df)}")
    print(f"前5行:")
    print(unmatched_df.head())
    
    # 读取统计信息sheet
    stats_df = pd.read_excel(result_file, sheet_name='统计信息')
    print(f"\n统计信息:")
    print(stats_df)
    
    # 检查数据完整性
    print(f"\n数据完整性检查:")
    matched_rows = df[df['匹配状态'] == '已匹配']
    unmatched_rows = df[df['匹配状态'] == '未匹配']
    empty_rows = df[df['匹配状态'] == '--- 空行分隔 ---']
    
    print(f"已匹配行数: {len(matched_rows)}")
    print(f"未匹配行数: {len(unmatched_rows)}")
    print(f"空行分隔行数: {len(empty_rows)}")
    print(f"总行数: {len(df)}")
    
    # 检查已匹配数据的完整性
    print(f"\n已匹配数据完整性:")
    matched_complete = matched_rows.dropna(subset=['一级编码', '一级部位', '二级编码', '二级部位', '三级编码'])
    print(f"完整匹配数据行数: {len(matched_complete)}")
    
    # 显示未匹配数据示例
    print(f"\n未匹配数据示例:")
    print(unmatched_rows[['DR三级部位', 'DR项目名称', '一级编码', '一级部位']].head())

except Exception as e:
    print(f"读取文件时出错: {e}")
    import traceback
    traceback.print_exc()
