#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析DR匹配问题：侧字符和眼眶匹配问题
"""

import pandas as pd
import numpy as np

def analyze_matching_issues():
    """分析匹配问题"""
    
    file_path = "data/DR项目结构-0706.xlsx"
    
    # 读取数据
    dr_data = pd.read_excel(file_path, sheet_name='DR')
    part_data = pd.read_excel(file_path, sheet_name='三级部位')
    
    # 清理数据
    dr_clean = dr_data.dropna(subset=['三级部位']).copy()
    part_clean = part_data.dropna(subset=['三级部位']).copy()
    
    print("=== 问题1：侧字符分析 ===")
    
    # 查找包含"侧"的DR三级部位
    dr_with_ce = dr_clean[dr_clean['三级部位'].str.contains('侧', na=False)]
    print(f"DR数据中包含'侧'的记录数: {len(dr_with_ce)}")
    print("包含'侧'的DR三级部位:")
    for part in sorted(dr_with_ce['三级部位'].unique()):
        print(f"  - {part}")
    
    # 查找包含"侧"的三级部位数据
    part_with_ce = part_clean[part_clean['三级部位'].str.contains('侧', na=False)]
    print(f"\n三级部位数据中包含'侧'的记录数: {len(part_with_ce)}")
    print("包含'侧'的三级部位:")
    for part in sorted(part_with_ce['三级部位'].unique()):
        print(f"  - {part}")
    
    print("\n=== 问题2：眼眶匹配分析 ===")
    
    # 查找眼眶相关记录
    dr_eye = dr_clean[dr_clean['三级部位'].str.contains('眼', na=False)]
    part_eye = part_clean[part_clean['三级部位'].str.contains('眼', na=False)]
    
    print(f"DR数据中包含'眼'的记录:")
    for part in sorted(dr_eye['三级部位'].unique()):
        print(f"  - '{part}' (长度: {len(part)})")
        # 显示字符编码
        print(f"    字符编码: {[ord(c) for c in part]}")
    
    print(f"\n三级部位数据中包含'眼'的记录:")
    for part in sorted(part_eye['三级部位'].unique()):
        print(f"  - '{part}' (长度: {len(part)})")
        # 显示字符编码
        print(f"    字符编码: {[ord(c) for c in part]}")
    
    # 精确比较眼眶
    dr_eye_exact = dr_clean[dr_clean['三级部位'] == '眼眶']['三级部位'].iloc[0] if len(dr_clean[dr_clean['三级部位'] == '眼眶']) > 0 else None
    part_eye_exact = part_clean[part_clean['三级部位'] == '眼眶']['三级部位'].iloc[0] if len(part_clean[part_clean['三级部位'] == '眼眶']) > 0 else None
    
    if dr_eye_exact and part_eye_exact:
        print(f"\n精确比较 '眼眶':")
        print(f"DR中的眼眶: '{dr_eye_exact}' == 三级部位中的眼眶: '{part_eye_exact}' ? {dr_eye_exact == part_eye_exact}")
        print(f"DR眼眶字节: {dr_eye_exact.encode('utf-8')}")
        print(f"三级部位眼眶字节: {part_eye_exact.encode('utf-8')}")
    
    # 检查是否有隐藏字符
    print(f"\n=== 检查隐藏字符 ===")
    
    # 检查所有三级部位的前后空格
    dr_parts_with_spaces = []
    for part in dr_clean['三级部位'].unique():
        if part != part.strip():
            dr_parts_with_spaces.append(part)
    
    part_parts_with_spaces = []
    for part in part_clean['三级部位'].unique():
        if part != part.strip():
            part_parts_with_spaces.append(part)
    
    print(f"DR数据中有前后空格的三级部位: {len(dr_parts_with_spaces)}")
    for part in dr_parts_with_spaces:
        print(f"  - '{part}' -> '{part.strip()}'")
    
    print(f"\n三级部位数据中有前后空格的三级部位: {len(part_parts_with_spaces)}")
    for part in part_parts_with_spaces:
        print(f"  - '{part}' -> '{part.strip()}'")
    
    return dr_clean, part_clean

if __name__ == "__main__":
    analyze_matching_issues()
