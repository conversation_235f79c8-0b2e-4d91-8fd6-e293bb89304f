#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的DR项目结构数据匹配处理脚本
按照具体要求：
1. 使用最新数据文件，保持字符串格式
2. 生成完整的129个部位列表
3. 处理摆位编码（体位+方向）
4. 生成DR检查项目编码
5. 按指定格式输出
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_all_data(file_path):
    """加载所有相关数据"""
    try:
        # 读取所有sheet
        dr_data = pd.read_excel(file_path, sheet_name='DR')
        part_data = pd.read_excel(file_path, sheet_name='三级部位')
        position_data = pd.read_excel(file_path, sheet_name='体位')
        direction_data = pd.read_excel(file_path, sheet_name='方向')

        print(f"DR数据: {dr_data.shape}")
        print(f"三级部位数据: {part_data.shape}")
        print(f"体位数据: {position_data.shape}")
        print(f"方向数据: {direction_data.shape}")

        return dr_data, part_data, position_data, direction_data
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None, None, None, None

def clean_part_name(part_name):
    """清理部位名称用于匹配"""
    if pd.isna(part_name):
        return part_name

    cleaned = str(part_name).strip()
    # 去除"侧"字符
    cleaned = cleaned.replace('侧', '')
    # 眼眶/眼框统一
    if cleaned == '眼框':
        cleaned = '眼眶'
    # 股骨髁间窝匹配到股骨
    if '股骨髁间窝' in cleaned:
        cleaned = cleaned.replace('股骨髁间窝', '股骨')

    return cleaned

def format_code(code, length):
    """格式化编码为指定长度的字符串"""
    if pd.isna(code):
        return ''
    # 确保转换为整数再转字符串，避免小数点
    if isinstance(code, (int, float)):
        code_str = str(int(code))
    else:
        code_str = str(code)
    return code_str.zfill(length)

def process_position_encoding(dr_data, position_data, direction_data):
    """处理摆位编码"""
    print("\n=== 处理摆位编码 ===")

    # 创建体位和方向的映射字典
    position_map = {}
    direction_map = {}

    # 处理体位数据 - 从体位sheet中读取
    for idx, row in position_data.iterrows():
        if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
            pos_code = str(row.iloc[0]).strip()  # 保持原始编码格式
            pos_name = str(row.iloc[1]).strip()
            position_map[pos_name] = pos_code

    # 处理方向数据 - 从方向sheet中读取
    for idx, row in direction_data.iterrows():
        if pd.notna(row.iloc[0]) and pd.notna(row.iloc[1]):
            dir_code = str(row.iloc[0]).strip()  # 保持原始编码格式
            dir_name = str(row.iloc[1]).strip()
            direction_map[dir_name] = dir_code

    print(f"体位映射: {len(position_map)}个")
    print(f"方向映射: {len(direction_map)}个")

    # 显示映射内容
    print(f"体位映射示例: {dict(list(position_map.items())[:5])}")
    print(f"方向映射示例: {dict(list(direction_map.items())[:5])}")

    return position_map, direction_map

def get_position_details_from_columns(dr_row, position_map, direction_map):
    """从DR数据行的列中获取体位和方向的详细信息"""

    # 定义体位列（从第4列开始到第27列，基于之前的分析）
    position_columns = ['仰卧位', '俯卧位', '左侧卧', '右侧卧', '斜位', '左前斜位', '右前斜位',
                       '左后斜位', '右后斜位', '站立位', '倒立位', '内斜位', '外斜位', '外展位',
                       '内收位', '过伸位', '过屈位', '张口位', '闭口位', '前弓位', '蛙形位', '全景', '应力位', '没有']

    # 定义方向列（从第28列开始）
    direction_columns = ['前后位', '后前位', '轴位', '前后轴位', '后前轴位', '切线位',
                        '左前后斜位', '右前后斜位', '右后前斜位', '左后前斜位', '左侧位',
                        '右侧位', '侧位', '穿胸位', '乳腺摄影', '头尾位', '内外斜位',
                        '内外位', '外内位', '尾头位', '左弯曲位', '右弯曲位', '轴斜位', '没有.1']

    # 默认值
    pos_code = '0'
    pos_name = ''
    dir_code = '0'
    dir_name = ''

    # 查找体位编码和名称
    for col in position_columns:
        if col in dr_row.index and pd.notna(dr_row[col]) and dr_row[col] == 1.0:
            if col in position_map:
                pos_code = position_map[col]
                pos_name = col
                break

    # 查找方向编码和名称
    for col in direction_columns:
        if col in dr_row.index and pd.notna(dr_row[col]) and dr_row[col] == 1.0:
            if col in direction_map:
                dir_code = direction_map[col]
                dir_name = col
                break

    return {
        '摆位编码': pos_code + dir_code,
        '体位': pos_name,
        '体位编码': pos_code,
        '方向': dir_name,
        '方向编码': dir_code
    }

def create_complete_output(dr_data, part_data, position_map, direction_map):
    """创建完整的输出数据"""
    print("\n=== 创建完整输出 ===")

    # 清理DR数据用于匹配
    dr_clean = dr_data.dropna(subset=['三级部位']).copy()
    dr_clean['三级部位_清理'] = dr_clean['三级部位'].apply(clean_part_name)

    # 清理三级部位数据
    part_clean = part_data.copy()
    part_clean['三级部位_清理'] = part_clean['三级部位'].apply(clean_part_name)

    print(f"DR清理后数据: {len(dr_clean)}行")
    print(f"三级部位数据: {len(part_clean)}行")

    # 创建最终输出列表
    output_records = []

    # 遍历所有129个三级部位
    for idx, part_row in part_clean.iterrows():
        # 基础部位信息（格式化为字符串）
        base_record = {
            '一级编码': format_code(part_row['一级编码'], 2),
            '一级部位': str(part_row['一级部位']) if pd.notna(part_row['一级部位']) else '',
            '二级编码': format_code(part_row['二级编码'], 2),
            '二级部位': str(part_row['二级部位']) if pd.notna(part_row['二级部位']) else '',
            '三级编码': format_code(part_row['三级编码'], 2),
            '三级部位': str(part_row['三级部位']) if pd.notna(part_row['三级部位']) else '',
            '部位编码': format_code(part_row['部位编码'], 6),
            '项目名称': '',
            'DR检查项目编码': '',
            '摆位': '',
            '摆位编码': '',
            '体位': '',
            '体位编码': '',
            '方向': '',
            '方向编码': ''
        }

        # 查找匹配的DR项目
        part_name_clean = part_row['三级部位_清理']
        matched_dr = dr_clean[dr_clean['三级部位_清理'] == part_name_clean]

        if len(matched_dr) > 0:
            # 有匹配的DR项目
            for dr_idx, dr_row in matched_dr.iterrows():
                record = base_record.copy()

                # 获取原始摆位信息并清理
                original_position = str(dr_row['摆位']) if pd.notna(dr_row['摆位']) else ''
                record['摆位'] = original_position

                # 获取摆位详细信息 - 从DR数据行的列中获取
                position_details = get_position_details_from_columns(dr_row, position_map, direction_map)

                # 填充摆位相关信息
                record['摆位编码'] = position_details['摆位编码']
                record['体位'] = position_details['体位']
                record['体位编码'] = position_details['体位编码']
                record['方向'] = position_details['方向']
                record['方向编码'] = position_details['方向编码']

                # 生成新的项目名称格式：DR+部位+"-"+摆位
                # 1. 清理部位名称：去除空格，中文括号改为英文括号
                part_name = record['三级部位'].replace(' ', '') if record['三级部位'] else ''
                part_name = part_name.replace('（', '(').replace('）', ')')

                # 2. 清理摆位名称：去除"DR"字符和多余空格，中文括号改为英文括号
                clean_position = original_position
                if clean_position:
                    # 去除"DR"字符（不区分大小写）
                    clean_position = clean_position.replace('DR', '').replace('dr', '')
                    # 去除多余空格
                    clean_position = clean_position.strip()
                    # 中文括号改为英文括号
                    clean_position = clean_position.replace('（', '(').replace('）', ')')

                # 3. 处理重复括号内容
                # 如果部位名称和摆位都包含相同的括号内容，只保留一个
                if part_name and clean_position and '(' in part_name and ')' in part_name:
                    # 提取部位名称中的括号内容
                    import re
                    part_brackets = re.findall(r'\([^)]*\)', part_name)
                    if part_brackets:
                        for bracket in part_brackets:
                            # 如果摆位中也包含相同的括号内容，从摆位中移除
                            if bracket in clean_position:
                                clean_position = clean_position.replace(bracket, '').strip()

                # 4. 生成项目名称
                if part_name and clean_position:
                    record['项目名称'] = f"DR{part_name}-{clean_position}"
                elif part_name:
                    record['项目名称'] = f"DR{part_name}"
                else:
                    record['项目名称'] = ''

                # 生成DR检查项目编码
                if record['部位编码'] and record['摆位编码']:
                    record['DR检查项目编码'] = f"DR{record['部位编码']}{record['摆位编码']}"

                output_records.append(record)
        else:
            # 没有匹配的DR项目，添加空记录
            output_records.append(base_record)

    return output_records

def main():
    """主函数"""
    file_path = "data/DR项目结构-0706.xlsx"

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    print("开始完整的DR项目结构数据匹配处理...")

    # 1. 加载所有数据
    dr_data, part_data, position_data, direction_data = load_all_data(file_path)
    if any(data is None for data in [dr_data, part_data, position_data, direction_data]):
        return

    # 2. 处理摆位编码
    position_map, direction_map = process_position_encoding(dr_data, position_data, direction_data)

    # 3. 创建完整输出
    output_records = create_complete_output(dr_data, part_data, position_map, direction_map)

    # 4. 转换为DataFrame并调整列顺序
    final_df = pd.DataFrame(output_records)

    # 确保列顺序：一级编码、一级部位、二级编码、二级部位、三级编码、三级部位、部位编码、项目名称、DR检查项目编码、摆位、摆位编码、体位、体位编码、方向、方向编码
    column_order = [
        '一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '部位编码',
        '项目名称', 'DR检查项目编码', '摆位', '摆位编码', '体位', '体位编码', '方向', '方向编码'
    ]
    final_df = final_df[column_order]

    # 确保编码列都是字符串格式
    for col in ['一级编码', '二级编码', '三级编码']:
        final_df[col] = final_df[col].astype(str)

    # 5. 统计信息
    total_parts = len(part_data)
    matched_parts = len(final_df[final_df['项目名称'] != ''])
    total_projects = len(final_df)

    print(f"\n=== 处理完成统计 ===")
    print(f"三级部位总数: {total_parts}")
    print(f"有DR项目的部位数: {len(final_df[final_df['项目名称'] != ''].groupby(['一级编码', '二级编码', '三级编码']))}")
    print(f"DR项目总数: {matched_parts}")
    print(f"输出记录总数: {total_projects}")

    # 6. 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output/DR完整匹配结果_{timestamp}.xlsx"

    os.makedirs("output", exist_ok=True)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主结果
        final_df.to_excel(writer, sheet_name='完整匹配结果', index=False)

        # 统计信息
        stats_data = {
            '统计项目': ['三级部位总数', '有DR项目的部位数', 'DR项目总数', '输出记录总数'],
            '数值': [total_parts, len(final_df[final_df['项目名称'] != ''].groupby(['一级编码', '二级编码', '三级编码'])), matched_parts, total_projects]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)

        # 部位覆盖情况
        coverage_data = []
        unique_parts = final_df.groupby(['一级编码', '二级编码', '三级编码', '三级部位']).agg({
            '项目名称': lambda x: len([i for i in x if i != ''])
        }).reset_index()

        for idx, row in unique_parts.iterrows():
            coverage_data.append({
                '一级编码': row['一级编码'],
                '二级编码': row['二级编码'],
                '三级编码': row['三级编码'],
                '三级部位': row['三级部位'],
                '是否有DR项目': '是' if row['项目名称'] > 0 else '否',
                'DR项目数量': row['项目名称']
            })

        coverage_df = pd.DataFrame(coverage_data)
        coverage_df.to_excel(writer, sheet_name='部位覆盖情况', index=False)

    print(f"\n结果已保存到: {output_file}")

    # 显示前几行结果
    print(f"\n前10行结果预览:")
    print(final_df.head(10)[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目名称', '摆位', 'DR检查项目编码']])

    return output_file

if __name__ == "__main__":
    main()
