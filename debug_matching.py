import pandas as pd

# 读取Excel文件
file_path = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0706.xlsx'

try:
    # 读取所有sheet
    excel_data = pd.read_excel(file_path, sheet_name=None)
    
    dr_sheet = excel_data['DR']
    level3_sheet = excel_data['三级部位']
    
    print("=== 调试匹配问题 ===")
    
    # 显示原始数据
    print(f"\nDR sheet原始三级部位列前20个值:")
    print(dr_sheet['三级部位'].head(20).tolist())
    
    print(f"\n三级部位 sheet原始三级部位列前20个值:")
    print(level3_sheet['三级部位'].head(20).tolist())
    
    # 清理数据
    dr_clean = dr_sheet['三级部位'].fillna('').astype(str).str.strip().str.replace(' ', '')
    level3_clean = level3_sheet['三级部位'].fillna('').astype(str).str.strip().str.replace(' ', '')
    
    # 过滤空值
    dr_clean = dr_clean[dr_clean != '']
    dr_clean = dr_clean[dr_clean != 'nan']
    level3_clean = level3_clean[level3_clean != '']
    level3_clean = level3_clean[level3_clean != 'nan']
    
    print(f"\nDR sheet清理后三级部位列前20个值:")
    print(dr_clean.head(20).tolist())
    
    print(f"\n三级部位 sheet清理后三级部位列前20个值:")
    print(level3_clean.head(20).tolist())
    
    # 检查匹配
    dr_unique = set(dr_clean.unique())
    level3_unique = set(level3_clean.unique())
    
    matched = dr_unique.intersection(level3_unique)
    unmatched = dr_unique - level3_unique
    
    print(f"\n匹配统计:")
    print(f"DR中唯一三级部位数量: {len(dr_unique)}")
    print(f"三级部位sheet中唯一三级部位数量: {len(level3_unique)}")
    print(f"可匹配数量: {len(matched)}")
    print(f"无法匹配数量: {len(unmatched)}")
    
    print(f"\n可匹配的三级部位 (前20个):")
    print(list(matched)[:20])
    
    print(f"\n无法匹配的三级部位 (前20个):")
    print(list(unmatched)[:20])
    
    # 检查是否有相似但不完全匹配的
    print(f"\n检查相似匹配:")
    for dr_part in list(unmatched)[:10]:
        similar = [l3_part for l3_part in level3_unique if dr_part in l3_part or l3_part in dr_part]
        if similar:
            print(f"DR: '{dr_part}' -> 相似: {similar}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()