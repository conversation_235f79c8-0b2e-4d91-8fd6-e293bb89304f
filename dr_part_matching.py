#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR项目结构数据匹配处理脚本
将DR sheet中的三级部位与三级部位sheet进行匹配，合并相关信息
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def load_excel_data(file_path):
    """加载Excel文件数据"""
    try:
        # 读取DR sheet
        dr_data = pd.read_excel(file_path, sheet_name='DR')
        print(f"DR sheet 数据形状: {dr_data.shape}")
        print(f"DR sheet 列名: {list(dr_data.columns)}")

        # 读取三级部位 sheet
        part_data = pd.read_excel(file_path, sheet_name='三级部位')
        print(f"三级部位 sheet 数据形状: {part_data.shape}")
        print(f"三级部位 sheet 列名: {list(part_data.columns)}")

        return dr_data, part_data
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None, None

def match_and_merge_data(dr_data, part_data):
    """匹配DR数据和三级部位数据并合并"""

    # 显示前几行数据以了解结构
    print("\nDR数据前5行:")
    print(dr_data.head())
    print("\n三级部位数据前5行:")
    print(part_data.head())

    # 确定匹配字段 - 都是"三级部位"列
    dr_part_col = '三级部位'
    part_name_col = '三级部位'

    print(f"\nDR数据匹配列: {dr_part_col}")
    print(f"三级部位数据匹配列: {part_name_col}")

    # 检查列是否存在
    if dr_part_col not in dr_data.columns:
        print(f"DR数据中未找到列: {dr_part_col}")
        return None, None

    if part_name_col not in part_data.columns:
        print(f"三级部位数据中未找到列: {part_name_col}")
        return None, None

    # 清理数据 - 移除空值行
    dr_clean = dr_data.dropna(subset=[dr_part_col]).copy()
    part_clean = part_data.dropna(subset=[part_name_col]).copy()

    print(f"\n清理后DR数据行数: {len(dr_clean)}")
    print(f"清理后三级部位数据行数: {len(part_clean)}")

    # 显示唯一的三级部位值
    print(f"\nDR数据中的唯一三级部位: {sorted(dr_clean[dr_part_col].unique())}")
    print(f"\n三级部位数据中的唯一三级部位: {sorted(part_clean[part_name_col].unique())}")

    # 执行左连接匹配
    merged_data = pd.merge(
        dr_clean,
        part_clean,
        left_on=dr_part_col,
        right_on=part_name_col,
        how='left',
        indicator=True,
        suffixes=('_DR', '_部位')
    )

    # 统计匹配结果
    match_stats = merged_data['_merge'].value_counts()
    print(f"\n匹配统计:")
    print(match_stats)

    # 找出未匹配的记录
    unmatched = merged_data[merged_data['_merge'] == 'left_only'].copy()
    if len(unmatched) > 0:
        print(f"\n未匹配的记录数: {len(unmatched)}")
        print("未匹配的三级部位:")
        unmatched_parts = unmatched[dr_part_col].unique()
        for part in sorted(unmatched_parts):
            if pd.notna(part):
                print(f"  - {part}")

    # 删除merge指示列
    merged_data = merged_data.drop('_merge', axis=1)

    return merged_data, unmatched

def create_final_output(merged_data, unmatched_data):
    """创建最终输出格式"""

    # 定义期望的列顺序
    desired_columns = [
        '三级部位_DR',  # DR中的三级部位
        '项目名称',     # DR中的项目名称
        '一级编码',     # 从三级部位表匹配来的
        '一级部位',     # 从三级部位表匹配来的
        '二级编码',     # 从三级部位表匹配来的
        '二级部位',     # 从三级部位表匹配来的
        '三级编码',     # 从三级部位表匹配来的
        '三级部位_部位', # 从三级部位表匹配来的
        '部位编码',     # 从三级部位表匹配来的
        'DR'           # 从三级部位表匹配来的
    ]

    # 创建最终数据框，只包含需要的列
    final_data = merged_data.copy()

    # 重新排列列顺序，只保留存在的列
    available_columns = [col for col in desired_columns if col in final_data.columns]
    other_columns = [col for col in final_data.columns if col not in desired_columns]

    # 最终列顺序：期望的列 + 其他列
    final_column_order = available_columns + other_columns
    final_data = final_data[final_column_order]

    # 为未匹配的数据添加标记
    if len(unmatched_data) > 0:
        # 创建一个标记列来识别未匹配的行
        final_data['匹配状态'] = '已匹配'
        unmatched_indices = unmatched_data.index
        final_data.loc[final_data.index.isin(unmatched_indices), '匹配状态'] = '未匹配'

        # 在未匹配行后插入空行
        result_list = []
        for idx, row in final_data.iterrows():
            result_list.append(row)
            if row['匹配状态'] == '未匹配':
                # 插入空行
                empty_row = pd.Series([np.nan] * len(final_data.columns), index=final_data.columns)
                empty_row['匹配状态'] = '空行分隔'
                result_list.append(empty_row)

        final_data = pd.DataFrame(result_list).reset_index(drop=True)

    return final_data

def main():
    """主函数"""
    file_path = "data/DR项目结构-0706.xlsx"

    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return

    print("开始处理DR项目结构数据...")

    # 加载数据
    dr_data, part_data = load_excel_data(file_path)
    if dr_data is None or part_data is None:
        return

    # 匹配和合并数据
    merged_data, unmatched_data = match_and_merge_data(dr_data, part_data)
    if merged_data is None:
        return

    # 创建最终输出
    final_data = create_final_output(merged_data, unmatched_data)

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output/DR部位匹配结果_{timestamp}.xlsx"

    os.makedirs("output", exist_ok=True)

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        final_data.to_excel(writer, sheet_name='匹配结果', index=False)

        if len(unmatched_data) > 0:
            unmatched_data.to_excel(writer, sheet_name='未匹配记录', index=False)

    print(f"\n处理完成！结果已保存到: {output_file}")
    print(f"总记录数: {len(final_data)}")
    print(f"未匹配记录数: {len(unmatched_data)}")

if __name__ == "__main__":
    main()
