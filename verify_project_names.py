#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_project_names():
    """验证项目名称格式"""

    # 读取结果文件
    result_file = 'output/DR完整匹配结果_20250706_050758.xlsx'
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')

    print("=== 项目名称格式验证 ===")

    # 获取有项目的记录
    has_projects = df[df['项目名称'] != '']
    print(f"有项目名称的记录数: {len(has_projects)}")

    # 检查项目名称格式
    print(f"\n项目名称格式检查:")
    print("期望格式: DR+部位+'-'+摆位")

    # 显示前15个项目名称示例
    print(f"\n项目名称示例:")
    for i, (idx, row) in enumerate(has_projects.head(15).iterrows()):
        part_name = row['三级部位']
        position = row['摆位']
        project_name = row['项目名称']
        expected_name = f"DR{part_name}-{position}"

        status = "✓" if project_name == expected_name else "✗"
        print(f"{i+1:2d}. {status} 部位:{part_name:<6} 摆位:{position:<12} "
              f"实际:{project_name}")

    # 统计格式正确性
    correct_count = 0
    total_count = len(has_projects)

    for idx, row in has_projects.iterrows():
        part_name = row['三级部位']
        position = row['摆位']
        project_name = row['项目名称']
        expected_name = f"DR{part_name}-{position}"

        if project_name == expected_name:
            correct_count += 1

    print(f"\n格式正确性统计:")
    print(f"格式正确的项目: {correct_count}/{total_count}")
    print(f"正确率: {correct_count/total_count*100:.1f}%")

    # 显示一些特殊的项目名称
    print(f"\n特殊项目名称示例:")
    unique_projects = [name for name in has_projects['项目名称'].unique() if pd.notna(name) and name != '']

    # 显示最长的项目名称
    longest_names = sorted(unique_projects, key=len, reverse=True)[:10]
    print("最长的项目名称:")
    for name in longest_names:
        print(f"  - {name} (长度: {len(name)})")

    # 显示包含特殊字符的项目名称
    special_names = [name for name in unique_projects if any(char in name for char in ['（', '）', '/', '\\', '+'])]
    if special_names:
        print(f"\n包含特殊字符的项目名称:")
        for name in special_names[:10]:
            print(f"  - {name}")
    else:
        print(f"\n未发现包含特殊字符的项目名称")

if __name__ == "__main__":
    verify_project_names()
