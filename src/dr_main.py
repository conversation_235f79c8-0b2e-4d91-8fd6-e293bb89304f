#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR检查项目清单生成主程序
"""

import pandas as pd
import os
from datetime import datetime
from dr_processor import DRProcessor

def main():
    """主函数"""
    print("="*60)
    print("DR检查项目清单生成程序")
    print("="*60)
    
    # 初始化处理器
    processor = DRProcessor()
    
    # 数据文件路径
    data_file = "data/DR项目结构-0705.xlsx"
    
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    # 加载数据
    print("📂 加载DR数据...")
    success, message = processor.load_data(data_file)
    
    if not success:
        print(f"❌ {message}")
        return
    
    print(f"✅ {message}")
    
    # 获取基本统计信息
    stats = processor.get_basic_stats()
    print(f"\n📊 数据统计:")
    print(f"  总记录数: {stats['total_records']}")
    print(f"  有效项目数: {stats['valid_projects']}")
    print(f"  一级部位数: {stats['level1_parts']}")
    print(f"  二级部位数: {stats['level2_parts']}")
    print(f"  三级部位数: {stats['level3_parts']}")
    print(f"  摆位类型数: {stats['positions']}")
    print(f"  方向编码数: {stats['direction_codes']}")
    print(f"  体位编码数: {stats['position_codes']}")
    
    # 生成DR检查项目清单
    print(f"\n🔧 生成DR检查项目清单...")
    dr_items = processor.generate_dr_items()
    
    if dr_items is None or len(dr_items) == 0:
        print("❌ 未能生成DR检查项目清单")
        return
    
    print(f"✅ 成功生成 {len(dr_items)} 个DR检查项目")
    
    # 显示生成结果统计
    print(f"\n📋 生成结果统计:")
    print(f"  DR项目总数: {len(dr_items)}")
    print(f"  一级部位分布:")
    for part, count in dr_items['一级部位'].value_counts().head(10).items():
        print(f"    {part}: {count}个")
    
    # 显示示例项目
    print(f"\n📝 示例项目（前10个）:")
    for i, (_, item) in enumerate(dr_items.head(10).iterrows()):
        print(f"  {i+1:2d}. {item['项目编码']} - {item['项目名称']}")
        print(f"      部位: {item['一级部位']}-{item['二级部位']}-{item['三级部位']}")
        print(f"      编码: {item['部位编码']} + {item['摆位编码']}")
    
    # 格式化输出
    formatted_items = processor.format_output_columns(dr_items)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = os.path.join(output_dir, f"DR检查项目清单_{timestamp}.xlsx")
    
    print(f"\n💾 保存结果到: {output_file}")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主要输出：标准格式的DR检查项目清单
        formatted_items.to_excel(writer, sheet_name='DR检查项目清单', index=False)
        
        # 详细信息：包含所有字段的完整数据
        dr_items.to_excel(writer, sheet_name='DR检查项目详细信息', index=False)
        
        # 部位编码字典
        part_dict = dr_items[['一级编码', '一级部位', '二级编码', '二级部位', 
                             '三级编码', '三级部位', '部位编码']].drop_duplicates()
        part_dict.to_excel(writer, sheet_name='DR部位编码字典', index=False)
        
        # 摆位编码字典
        position_dict = dr_items[['摆位编码', '方向编码', '体位编码', 
                                 '原始摆位', '清理后摆位']].drop_duplicates()
        position_dict.to_excel(writer, sheet_name='DR摆位编码字典', index=False)
        
        # 统计信息
        stats_df = pd.DataFrame([
            ['总记录数', stats['total_records']],
            ['有效项目数', stats['valid_projects']],
            ['生成项目数', len(dr_items)],
            ['一级部位数', stats['level1_parts']],
            ['二级部位数', stats['level2_parts']],
            ['三级部位数', stats['level3_parts']],
            ['摆位类型数', stats['positions']],
            ['方向编码数', stats['direction_codes']],
            ['体位编码数', stats['position_codes']]
        ], columns=['统计项目', '数值'])
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        # 列格式说明
        format_info = pd.DataFrame([
            ['模态', 'DR', '检查模态标识'],
            ['一级编码', '01-07', '一级部位编码（2位）'],
            ['一级部位', '头部/胸部等', '一级部位名称'],
            ['二级编码', '01-99', '二级部位编码（2位）'],
            ['二级部位', '颅脑/胸部等', '二级部位名称'],
            ['三级编码', '01-99', '三级部位编码（2位）'],
            ['三级部位', '头颅/胸部等', '三级部位名称'],
            ['项目编码', 'DR010101_0_0', 'DR+部位编码+摆位编码'],
            ['项目名称', 'DR头颅正位', '标准化项目名称']
        ], columns=['字段名', '示例值', '说明'])
        format_info.to_excel(writer, sheet_name='列格式说明', index=False)
    
    print(f"✅ 结果已保存，包含以下工作表:")
    print(f"  - DR检查项目清单: 标准格式的项目清单")
    print(f"  - DR检查项目详细信息: 包含所有字段的完整数据")
    print(f"  - DR部位编码字典: 部位编码对照表")
    print(f"  - DR摆位编码字典: 摆位编码对照表")
    print(f"  - 统计信息: 数据统计报告")
    print(f"  - 列格式说明: 字段格式说明")
    
    # 质量验证
    print(f"\n🔍 质量验证:")
    
    # 检查编码唯一性
    duplicate_codes = dr_items[dr_items.duplicated(['项目编码'], keep=False)]
    if len(duplicate_codes) > 0:
        print(f"  ⚠️  发现 {len(duplicate_codes)} 个重复的项目编码")
    else:
        print(f"  ✅ 项目编码唯一性检查通过")
    
    # 检查必填字段
    required_fields = ['模态', '一级部位', '二级部位', '三级部位', '项目编码', '项目名称']
    missing_data = []
    for field in required_fields:
        missing_count = dr_items[field].isna().sum()
        if missing_count > 0:
            missing_data.append(f"{field}: {missing_count}个缺失")
    
    if missing_data:
        print(f"  ⚠️  发现缺失数据: {', '.join(missing_data)}")
    else:
        print(f"  ✅ 必填字段完整性检查通过")
    
    # 检查编码格式
    invalid_codes = dr_items[~dr_items['项目编码'].str.match(r'^DR\d{6}[A-Z0-9_]+$')]
    if len(invalid_codes) > 0:
        print(f"  ⚠️  发现 {len(invalid_codes)} 个格式不正确的项目编码")
    else:
        print(f"  ✅ 项目编码格式检查通过")
    
    print(f"\n🎉 DR检查项目清单生成完成！")
    print(f"输出文件: {output_file}")

if __name__ == "__main__":
    main()
