#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR可视化组件
为Streamlit提供DR处理相关的可视化界面组件
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, Optional


class DRVisualization:
    """DR可视化组件"""

    @staticmethod
    def display_basic_stats(stats: Dict):
        """显示基础统计信息"""
        if not stats:
            st.warning("暂无统计数据")
            return

        st.subheader("DR数据基础统计")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总记录数", stats.get('total_records', 0))
            st.metric("有效项目数", stats.get('valid_projects', 0))

        with col2:
            st.metric("一级部位数", stats.get('level1_parts', 0))
            st.metric("二级部位数", stats.get('level2_parts', 0))

        with col3:
            st.metric("三级部位数", stats.get('level3_parts', 0))
            st.metric("摆位类型数", stats.get('positions', 0))

        with col4:
            st.metric("方向编码数", stats.get('direction_codes', 0))
            st.metric("体位编码数", stats.get('position_codes', 0))

    @staticmethod
    def display_mapping_statistics(mapping_stats: Dict):
        """显示映射统计信息"""
        if not mapping_stats:
            st.warning("暂无映射统计数据")
            return

        st.subheader("部位映射统计")

        # DR到标准字典的映射统计
        dr_to_std = mapping_stats.get('dr_to_standard', {})
        if dr_to_std:
            st.write("**DR部位 → 标准字典映射**")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("总DR部位数", dr_to_std.get('总DR部位数', 0))
            with col2:
                st.metric("映射成功", dr_to_std.get('映射成功', 0))
            with col3:
                st.metric("模糊匹配", dr_to_std.get('模糊匹配', 0))
            with col4:
                st.metric("映射失败", dr_to_std.get('映射失败', 0))

            # 成功率显示
            success_rate = dr_to_std.get('成功率', 0)
            if success_rate >= 80:
                st.success(f"✅ 映射成功率：{success_rate:.1f}%")
            elif success_rate >= 60:
                st.warning(f"⚠️ 映射成功率：{success_rate:.1f}%")
            else:
                st.error(f"❌ 映射成功率：{success_rate:.1f}%")

        # 标准字典到DR的映射统计
        std_to_dr = mapping_stats.get('standard_to_dr', {})
        if std_to_dr:
            st.write("**标准字典 → DR数据覆盖**")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("标准DR适用部位", std_to_dr.get('标准DR适用部位数', 0))
            with col2:
                st.metric("DR数据中存在", std_to_dr.get('DR数据中存在', 0))
            with col3:
                st.metric("DR数据中缺失", std_to_dr.get('DR数据中缺失', 0))

            # 覆盖率显示
            coverage_rate = std_to_dr.get('覆盖率', 0)
            if coverage_rate >= 80:
                st.success(f"✅ 覆盖率：{coverage_rate:.1f}%")
            elif coverage_rate >= 60:
                st.warning(f"⚠️ 覆盖率：{coverage_rate:.1f}%")
            else:
                st.error(f"❌ 覆盖率：{coverage_rate:.1f}%")

    @staticmethod
    def display_mapping_charts(mapping_stats: Dict):
        """显示映射统计图表"""
        if not mapping_stats:
            return

        col1, col2 = st.columns(2)

        # DR到标准字典映射饼图
        dr_to_std = mapping_stats.get('dr_to_standard', {})
        if dr_to_std:
            with col1:
                st.write("**DR部位映射分布**")

                labels = ['映射成功', '模糊匹配', '映射失败']
                values = [
                    dr_to_std.get('映射成功', 0),
                    dr_to_std.get('模糊匹配', 0),
                    dr_to_std.get('映射失败', 0)
                ]
                colors = ['#2E8B57', '#FFD700', '#DC143C']

                fig = go.Figure(data=[go.Pie(
                    labels=labels,
                    values=values,
                    marker_colors=colors,
                    hole=0.3
                )])
                fig.update_layout(
                    title="DR部位映射状态分布",
                    height=400
                )
                st.plotly_chart(fig, use_container_width=True)

        # 标准字典覆盖率饼图
        std_to_dr = mapping_stats.get('standard_to_dr', {})
        if std_to_dr:
            with col2:
                st.write("**标准字典覆盖分布**")

                labels = ['DR数据中存在', 'DR数据中缺失']
                values = [
                    std_to_dr.get('DR数据中存在', 0),
                    std_to_dr.get('DR数据中缺失', 0)
                ]
                colors = ['#2E8B57', '#DC143C']

                fig = go.Figure(data=[go.Pie(
                    labels=labels,
                    values=values,
                    marker_colors=colors,
                    hole=0.3
                )])
                fig.update_layout(
                    title="标准字典DR部位覆盖分布",
                    height=400
                )
                st.plotly_chart(fig, use_container_width=True)

    @staticmethod
    def display_difference_details(diff_details: Dict):
        """显示差异详情"""
        if not diff_details:
            st.warning("暂无差异详情数据")
            return

        st.subheader("差异详情分析")

        # 创建标签页
        tab_names = []
        if not diff_details['missing_in_standard'].empty:
            tab_names.append("DR部位缺失")
        if not diff_details['missing_in_dr'].empty:
            tab_names.append("标准部位缺失")
        if not diff_details['fuzzy_matches'].empty:
            tab_names.append("模糊匹配")
        if not diff_details['dr_not_applicable'].empty:
            tab_names.append("DR不适用")

        if not tab_names:
            st.info("✅ 未发现映射差异问题")
            return

        tabs = st.tabs(tab_names)
        tab_index = 0

        # DR部位缺失
        if not diff_details['missing_in_standard'].empty:
            with tabs[tab_index]:
                st.write("**DR数据中存在但标准字典中缺失的部位**")
                missing_std = diff_details['missing_in_standard']

                # 显示统计
                st.info(f"发现 {len(missing_std)} 个DR部位在标准字典中缺失")

                # 显示详情表格
                display_cols = ['DR项目名称', 'DR一级部位', 'DR二级部位', 'DR三级部位', '错误信息']
                # 确保列存在
                available_cols = [col for col in display_cols if col in missing_std.columns]
                st.dataframe(missing_std[available_cols], use_container_width=True)

                # 处理建议
                st.warning("**处理建议**：需要在标准字典中添加这些部位，或修正DR数据中的部位名称")
            tab_index += 1

        # 标准部位缺失
        if not diff_details['missing_in_dr'].empty:
            with tabs[tab_index]:
                st.write("**标准字典中标记为DR适用但DR数据中缺失的部位**")
                missing_dr = diff_details['missing_in_dr']

                # 显示统计
                st.info(f"发现 {len(missing_dr)} 个标准部位在DR数据中缺失")

                # 显示详情表格
                display_cols = ['标准一级部位', '标准二级部位', '标准三级部位', '标准部位编码', '缺失原因']
                st.dataframe(missing_dr[display_cols], use_container_width=True)

                # 处理建议
                st.warning("**处理建议**：需要在DR数据中添加相应的检查项目，或修正标准字典的DR适用性标记")
            tab_index += 1

        # 模糊匹配
        if not diff_details['fuzzy_matches'].empty:
            with tabs[tab_index]:
                st.write("**需要人工确认的模糊匹配部位**")
                fuzzy = diff_details['fuzzy_matches']

                # 显示统计
                st.info(f"发现 {len(fuzzy)} 个模糊匹配的部位")

                # 显示详情表格
                display_cols = [
                    'DR项目名称', 'DR一级部位', 'DR二级部位', 'DR三级部位',
                    '标准一级部位', '标准二级部位', '标准三级部位',
                    '相似度', '错误信息'
                ]
                # 确保列存在
                available_cols = [col for col in display_cols if col in fuzzy.columns]
                st.dataframe(fuzzy[available_cols], use_container_width=True)

                # 处理建议
                st.warning("**处理建议**：请人工确认这些模糊匹配是否正确，必要时调整部位名称")
            tab_index += 1

        # DR不适用
        if not diff_details['dr_not_applicable'].empty:
            with tabs[tab_index]:
                st.write("**映射成功但标准字典中标记为不适用DR的部位**")
                not_applicable = diff_details['dr_not_applicable']

                # 显示统计
                st.info(f"发现 {len(not_applicable)} 个DR不适用的部位")

                # 显示详情表格
                display_cols = [
                    'DR项目名称', 'DR一级部位', 'DR二级部位', 'DR三级部位',
                    '标准一级部位', '标准二级部位', '标准三级部位',
                    'DR适用性'
                ]
                # 确保列存在
                available_cols = [col for col in display_cols if col in not_applicable.columns]
                st.dataframe(not_applicable[available_cols], use_container_width=True)

                # 处理建议
                st.error("**处理建议**：这些部位在标准字典中标记为不适用DR检查，请确认DR数据的正确性")

    @staticmethod
    def display_dr_items_preview(dr_items: pd.DataFrame, show_count: int = 20):
        """显示DR项目预览"""
        if dr_items is None or len(dr_items) == 0:
            st.warning("暂无DR项目数据")
            return

        st.subheader("DR检查项目预览")

        # 统计信息
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("项目总数", len(dr_items))
        with col2:
            st.metric("一级部位数", len(dr_items['一级部位'].unique()))
        with col3:
            st.metric("二级部位数", len(dr_items['二级部位'].unique()))
        with col4:
            st.metric("三级部位数", len(dr_items['三级部位'].unique()))

        # 筛选选项
        col1, col2 = st.columns(2)
        with col1:
            selected_part = st.selectbox(
                "选择一级部位",
                ["全部"] + list(dr_items['一级部位'].unique())
            )
        with col2:
            show_count = st.slider("显示项目数量", 10, 100, show_count)

        # 筛选数据
        if selected_part != "全部":
            filtered_items = dr_items[dr_items['一级部位'] == selected_part]
        else:
            filtered_items = dr_items

        # 显示表格
        st.info("列顺序：模态 → 一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 项目编码 → 项目名称")

        # 标准列顺序
        standard_columns = [
            '模态', '一级编码', '一级部位', '二级编码', '二级部位',
            '三级编码', '三级部位', '项目编码', '项目名称'
        ]

        # 确保列存在
        display_df = filtered_items.copy()
        for col in standard_columns:
            if col not in display_df.columns:
                display_df[col] = ''

        st.dataframe(display_df[standard_columns].head(show_count), use_container_width=True)

        # 项目示例
        if len(filtered_items) > 0:
            st.write("**项目示例**")
            for i, (_, row) in enumerate(filtered_items.head(5).iterrows()):
                st.code(f"{row['项目编码']} - {row['项目名称']}")
                if i == 0:
                    # 显示详细信息
                    details = []
                    if '部位编码' in row:
                        details.append(f"部位编码: {row['部位编码']}")
                    if '摆位编码' in row:
                        details.append(f"摆位编码: {row['摆位编码']}")
                    if '映射状态' in row:
                        details.append(f"映射状态: {row['映射状态']}")
                    if details:
                        st.caption(" | ".join(details))

    @staticmethod
    def display_part_distribution(dr_items: pd.DataFrame):
        """显示部位分布统计"""
        if dr_items is None or len(dr_items) == 0:
            return

        st.subheader("部位分布统计")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**一级部位分布**")
            part_stats = dr_items['一级部位'].value_counts()
            for part, count in part_stats.items():
                st.write(f"- {part}: {count}个项目")

        with col2:
            st.write("**摆位编码分布（前10个）**")
            if '摆位编码' in dr_items.columns:
                pose_stats = dr_items['摆位编码'].value_counts().head(10)
                for pose, count in pose_stats.items():
                    st.write(f"- {pose}: {count}个项目")

    @staticmethod
    def create_download_button(buffer, filename: str, label: str = "下载Excel报告"):
        """创建下载按钮"""
        if buffer and hasattr(buffer, 'getvalue'):
            st.download_button(
                label=label,
                data=buffer.getvalue(),
                file_name=filename,
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
