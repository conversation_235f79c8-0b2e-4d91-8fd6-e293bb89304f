#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR处理管理器
整合DR数据加载、验证、映射和项目生成的完整流程
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional
from io import BytesIO

from dr_processor import DRProcessor
from dr_mapping_validator import DRMappingValidator


class DRManager:
    """DR处理管理器"""
    
    def __init__(self):
        self.dr_df = None
        self.direction_df = None
        self.position_df = None
        self.three_level_dict = None
        
        # 子模块
        self.processor = DRProcessor()
        self.validator = DRMappingValidator()
        
        # 处理结果
        self.mapping_validation_completed = False
        self.dr_items = None
        self.mapping_results = None
        self.difference_analysis = None
        
    def load_dr_data(self, file_path_or_buffer) -> Tuple[bool, str]:
        """加载DR数据文件"""
        try:
            # 读取DR数据
            self.dr_df = pd.read_excel(file_path_or_buffer, sheet_name='DR')
            self.direction_df = pd.read_excel(file_path_or_buffer, sheet_name='方向')
            self.position_df = pd.read_excel(file_path_or_buffer, sheet_name='体位')
            
            # 设置到子模块
            self.processor.dr_df = self.dr_df
            self.processor.direction_df = self.direction_df
            self.processor.position_df = self.position_df
            
            # 创建编码映射
            if self.direction_df is not None:
                self.processor.direction_mapping = dict(zip(
                    self.direction_df['方向'], 
                    self.direction_df['方向编码']
                ))
            
            if self.position_df is not None:
                self.processor.position_mapping = dict(zip(
                    self.position_df['体位'], 
                    self.position_df['体位编码']
                ))
            
            # 加载到验证器
            self.validator.load_dr_data(self.dr_df)
            
            return True, f"成功加载DR数据：{len(self.dr_df)}条记录"
            
        except Exception as e:
            return False, f"DR数据加载失败：{str(e)}"
    
    def set_standard_dict(self, three_level_dict: pd.DataFrame) -> Tuple[bool, str]:
        """设置标准三级部位字典"""
        try:
            self.three_level_dict = three_level_dict.copy()
            
            # 设置到子模块
            self.processor.set_three_level_dict(three_level_dict)
            self.validator.load_standard_dict(three_level_dict)
            
            return True, "标准字典设置成功"
            
        except Exception as e:
            return False, f"标准字典设置失败：{str(e)}"
    
    def validate_part_mapping(self) -> Tuple[bool, str]:
        """执行部位映射验证"""
        if self.dr_df is None or self.three_level_dict is None:
            return False, "数据未完整加载"
        
        try:
            # 执行双向映射验证
            success, message = self.validator.perform_bidirectional_mapping()
            
            if success:
                self.mapping_validation_completed = True
                self.mapping_results = self.validator.mapping_results
                self.difference_analysis = self.validator.difference_analysis
                
                # 获取统计信息
                stats = self.validator.get_mapping_statistics()
                dr_stats = stats.get('dr_to_standard', {})
                std_stats = stats.get('standard_to_dr', {})
                
                summary = (
                    f"部位映射验证完成\n"
                    f"DR→标准：成功{dr_stats.get('映射成功', 0)}个，"
                    f"模糊匹配{dr_stats.get('模糊匹配', 0)}个，"
                    f"失败{dr_stats.get('映射失败', 0)}个\n"
                    f"标准→DR：覆盖{std_stats.get('DR数据中存在', 0)}个，"
                    f"缺失{std_stats.get('DR数据中缺失', 0)}个"
                )
                
                return True, summary
            else:
                return False, message
                
        except Exception as e:
            return False, f"映射验证失败：{str(e)}"
    
    def generate_dr_items(self, use_standard_codes: bool = True) -> Tuple[Optional[pd.DataFrame], Optional[str]]:
        """生成DR检查项目清单"""
        if self.dr_df is None:
            return None, "DR数据未加载"
        
        try:
            if use_standard_codes:
                if not self.mapping_validation_completed:
                    return None, "请先完成部位映射验证"
                
                # 使用验证后的映射结果
                self.processor.part_mapping_results = self.mapping_results
            
            # 生成DR项目清单
            self.dr_items = self.processor.generate_dr_items()
            
            if self.dr_items is not None and len(self.dr_items) > 0:
                return self.dr_items, f"成功生成{len(self.dr_items)}个DR检查项目"
            else:
                return None, "DR项目生成失败"
                
        except Exception as e:
            return None, f"DR项目生成失败：{str(e)}"
    
    def get_basic_stats(self) -> Dict:
        """获取基本统计信息"""
        stats = {}
        
        if self.dr_df is not None:
            stats.update({
                'total_records': len(self.dr_df),
                'valid_projects': len(self.dr_df[self.dr_df['项目名称'].notna()]),
                'level1_parts': len(self.dr_df['一级部位'].unique()) if '一级部位' in self.dr_df.columns else 0,
                'level2_parts': len(self.dr_df['二级部位'].unique()) if '二级部位' in self.dr_df.columns else 0,
                'level3_parts': len(self.dr_df['三级部位'].unique()) if '三级部位' in self.dr_df.columns else 0,
                'positions': len(self.dr_df['摆位'].unique()) if '摆位' in self.dr_df.columns else 0,
                'direction_codes': len(self.direction_df) if self.direction_df is not None else 0,
                'position_codes': len(self.position_df) if self.position_df is not None else 0
            })
        
        return stats
    
    def get_mapping_statistics(self) -> Dict:
        """获取映射统计信息"""
        if not self.mapping_validation_completed:
            return {}
        
        return self.validator.get_mapping_statistics()
    
    def get_difference_details(self) -> Dict:
        """获取差异详情"""
        if not self.mapping_validation_completed:
            return {}
        
        return self.validator.get_difference_details()
    
    def export_difference_report(self) -> Optional[pd.DataFrame]:
        """导出差异分析报告"""
        if not self.mapping_validation_completed:
            return None
        
        return self.validator.export_difference_report()
    
    def export_comprehensive_report(self) -> BytesIO:
        """导出综合Excel报告"""
        buffer = BytesIO()
        
        try:
            with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                # 1. DR检查项目清单（如果已生成）
                if self.dr_items is not None:
                    # 标准格式输出
                    standard_columns = [
                        '模态', '一级编码', '一级部位', '二级编码', '二级部位',
                        '三级编码', '三级部位', '项目编码', '项目名称'
                    ]
                    dr_items_standard = self.dr_items[standard_columns].copy()
                    dr_items_standard.to_excel(writer, sheet_name='DR检查项目清单', index=False)
                    
                    # 详细信息
                    self.dr_items.to_excel(writer, sheet_name='DR检查项目详细信息', index=False)
                
                # 2. 部位映射结果（如果已验证）
                if self.mapping_validation_completed:
                    self.mapping_results.to_excel(writer, sheet_name='DR部位映射结果', index=False)
                    
                    # 差异详情
                    diff_details = self.get_difference_details()
                    
                    if not diff_details['missing_in_standard'].empty:
                        diff_details['missing_in_standard'].to_excel(
                            writer, sheet_name='DR部位缺失详情', index=False
                        )
                    
                    if not diff_details['missing_in_dr'].empty:
                        diff_details['missing_in_dr'].to_excel(
                            writer, sheet_name='标准部位缺失详情', index=False
                        )
                    
                    if not diff_details['fuzzy_matches'].empty:
                        diff_details['fuzzy_matches'].to_excel(
                            writer, sheet_name='模糊匹配详情', index=False
                        )
                    
                    # 差异分析报告
                    diff_report = self.export_difference_report()
                    if diff_report is not None and not diff_report.empty:
                        diff_report.to_excel(writer, sheet_name='差异分析报告', index=False)
                
                # 3. 基础数据表
                if self.dr_df is not None:
                    self.dr_df.to_excel(writer, sheet_name='DR原始数据', index=False)
                
                if self.direction_df is not None:
                    self.direction_df.to_excel(writer, sheet_name='方向编码表', index=False)
                
                if self.position_df is not None:
                    self.position_df.to_excel(writer, sheet_name='体位编码表', index=False)
                
                # 4. 统计信息
                stats_data = []
                
                # 基础统计
                basic_stats = self.get_basic_stats()
                for key, value in basic_stats.items():
                    stats_data.append(['基础统计', key, value])
                
                # 映射统计
                if self.mapping_validation_completed:
                    mapping_stats = self.get_mapping_statistics()
                    for category, stats in mapping_stats.items():
                        for key, value in stats.items():
                            stats_data.append(['映射统计', f"{category}_{key}", value])
                
                if stats_data:
                    stats_df = pd.DataFrame(stats_data, columns=['类别', '统计项目', '数值'])
                    stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                
                # 5. 处理说明
                instructions = pd.DataFrame({
                    '工作表名称': [
                        'DR检查项目清单',
                        'DR检查项目详细信息',
                        'DR部位映射结果',
                        'DR部位缺失详情',
                        '标准部位缺失详情',
                        '模糊匹配详情',
                        '差异分析报告',
                        'DR原始数据',
                        '方向编码表',
                        '体位编码表',
                        '统计信息'
                    ],
                    '说明': [
                        '标准9列格式的DR检查项目清单',
                        '包含所有字段的完整DR项目信息',
                        'DR部位到标准字典的映射结果',
                        'DR数据中存在但标准字典中缺失的部位',
                        '标准字典中标记为DR适用但DR数据中缺失的部位',
                        '需要人工确认的模糊匹配部位',
                        '综合差异分析和处理建议',
                        'DR检查项目原始数据',
                        'DR方向编码对照表',
                        'DR体位编码对照表',
                        '各类统计信息汇总'
                    ]
                })
                instructions.to_excel(writer, sheet_name='使用说明', index=False)
            
            buffer.seek(0)
            return buffer
            
        except Exception as e:
            print(f"导出报告失败：{str(e)}")
            return BytesIO()
    
    def format_output_columns(self, items_df: pd.DataFrame) -> pd.DataFrame:
        """格式化输出列顺序为标准格式"""
        if items_df is None or len(items_df) == 0:
            return items_df
        
        # 标准输出列顺序
        standard_columns = [
            '模态', '一级编码', '一级部位', '二级编码', '二级部位',
            '三级编码', '三级部位', '项目编码', '项目名称'
        ]
        
        # 确保所有标准列都存在
        for col in standard_columns:
            if col not in items_df.columns:
                items_df[col] = ''
        
        # 返回按标准顺序排列的DataFrame
        return items_df[standard_columns]
