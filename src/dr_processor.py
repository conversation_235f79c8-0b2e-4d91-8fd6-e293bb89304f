#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR检查项目处理器
实现DR检查项目清单的生成，包括部位编码和摆位编码的处理
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re
import os

class DRProcessor:
    """DR检查项目处理器"""

    def __init__(self):
        self.dr_df = None
        self.direction_df = None
        self.position_df = None
        self.direction_mapping = {}
        self.position_mapping = {}
        # 新增：三级部位字典表用于部位编码统一
        self.three_level_dict = None
        self.part_mapping_results = None

    def load_data(self, file_path):
        """加载DR数据文件"""
        try:
            # 读取各个工作表
            self.dr_df = pd.read_excel(file_path, sheet_name='DR')
            self.direction_df = pd.read_excel(file_path, sheet_name='方向')
            self.position_df = pd.read_excel(file_path, sheet_name='体位')

            # 创建编码映射
            self.direction_mapping = dict(zip(self.direction_df['方向'], self.direction_df['方向编码']))
            self.position_mapping = dict(zip(self.position_df['体位'], self.position_df['体位编码']))

            return True, f"成功加载DR数据：{len(self.dr_df)}条记录"
        except Exception as e:
            return False, f"数据加载失败：{str(e)}"

    def set_three_level_dict(self, three_level_dict):
        """设置三级部位字典表用于部位编码统一"""
        self.three_level_dict = three_level_dict

    def map_dr_parts_to_standard(self):
        """将DR部位映射到标准三级部位字典"""
        if self.dr_df is None or self.three_level_dict is None:
            return False, "DR数据或三级部位字典未加载"

        mapping_results = []
        successful_mappings = 0
        failed_mappings = 0

        # 创建三级部位字典的查找索引
        dict_lookup = {}
        for _, row in self.three_level_dict.iterrows():
            key = f"{row['一级部位']}|{row['二级部位']}|{row['三级部位']}"
            dict_lookup[key] = {
                '一级编码': row['一级编码'],
                '一级部位': row['一级部位'],
                '二级编码': row['二级编码'],
                '二级部位': row['二级部位'],
                '三级编码': row['三级编码'],
                '三级部位': row['三级部位'],
                '部位编码': row['部位编码'],
                'DR适用': row.get('DR适用', '否')
            }

        # 处理每个DR数据行
        for idx, row in self.dr_df.iterrows():
            dr_level1 = str(row.get('一级部位', '')).strip()
            dr_level2 = str(row.get('二级部位', '')).strip()
            dr_level3 = str(row.get('三级部位', '')).strip()
            project_name = str(row.get('项目名称', '')).strip()

            # 跳过无效数据
            if pd.isna(project_name) or project_name == '' or project_name == 'nan':
                continue

            # 构建查找键
            lookup_key = f"{dr_level1}|{dr_level2}|{dr_level3}"

            mapping_result = {
                'DR行号': idx + 1,
                'DR项目名称': project_name,
                'DR一级部位': dr_level1,
                'DR二级部位': dr_level2,
                'DR三级部位': dr_level3,
                '映射状态': '',
                '标准一级编码': '',
                '标准一级部位': '',
                '标准二级编码': '',
                '标准二级部位': '',
                '标准三级编码': '',
                '标准三级部位': '',
                '标准部位编码': '',
                'DR适用性': '',
                '错误信息': ''
            }

            # 尝试精确匹配
            if lookup_key in dict_lookup:
                std_part = dict_lookup[lookup_key]
                mapping_result.update({
                    '映射状态': '成功',
                    '标准一级编码': std_part['一级编码'],
                    '标准一级部位': std_part['一级部位'],
                    '标准二级编码': std_part['二级编码'],
                    '标准二级部位': std_part['二级部位'],
                    '标准三级编码': std_part['三级编码'],
                    '标准三级部位': std_part['三级部位'],
                    '标准部位编码': std_part['部位编码'],
                    'DR适用性': std_part['DR适用']
                })
                successful_mappings += 1

                # 检查DR适用性
                if std_part['DR适用'] != '是':
                    mapping_result['错误信息'] = '警告：该部位在标准字典中标记为不适用DR检查'
            else:
                # 尝试模糊匹配
                fuzzy_match = self._fuzzy_match_part(dr_level1, dr_level2, dr_level3, dict_lookup)
                if fuzzy_match:
                    mapping_result.update({
                        '映射状态': '模糊匹配',
                        '标准一级编码': fuzzy_match['一级编码'],
                        '标准一级部位': fuzzy_match['一级部位'],
                        '标准二级编码': fuzzy_match['二级编码'],
                        '标准二级部位': fuzzy_match['二级部位'],
                        '标准三级编码': fuzzy_match['三级编码'],
                        '标准三级部位': fuzzy_match['三级部位'],
                        '标准部位编码': fuzzy_match['部位编码'],
                        'DR适用性': fuzzy_match['DR适用'],
                        '错误信息': f'模糊匹配到：{fuzzy_match["一级部位"]}-{fuzzy_match["二级部位"]}-{fuzzy_match["三级部位"]}'
                    })
                    successful_mappings += 1
                else:
                    mapping_result.update({
                        '映射状态': '失败',
                        '错误信息': f'未找到匹配的标准部位：{dr_level1}-{dr_level2}-{dr_level3}'
                    })
                    failed_mappings += 1

            mapping_results.append(mapping_result)

        self.part_mapping_results = pd.DataFrame(mapping_results)

        return True, f"部位映射完成：成功{successful_mappings}个，失败{failed_mappings}个"

    def _fuzzy_match_part(self, dr_level1, dr_level2, dr_level3, dict_lookup):
        """模糊匹配部位"""
        # 简单的模糊匹配逻辑
        for key, std_part in dict_lookup.items():
            std_level1, std_level2, std_level3 = key.split('|')

            # 检查三级部位是否包含关键词
            if dr_level3 in std_level3 or std_level3 in dr_level3:
                if dr_level1 == std_level1:  # 一级部位必须匹配
                    return std_part

        return None

    def generate_part_code(self, level1_part, level2_part, level3_part):
        """生成六位部位编码"""
        # 一级部位编码映射
        level1_mapping = {
            '头部': '01',
            '颈部': '02',
            '脊柱': '03',
            '胸部': '04',
            '腹部': '05',
            '盆部': '06',
            '四肢及关节': '07'
        }

        # 获取一级编码
        level1_code = level1_mapping.get(level1_part, '99')

        # 生成二级和三级编码
        level2_code = self._get_or_create_part_code(level2_part, level1_part, 'level2')
        level3_code = self._get_or_create_part_code(level3_part, f"{level1_part}_{level2_part}", 'level3')

        return f"{level1_code}{level2_code}{level3_code}"

    def _get_or_create_part_code(self, part_name, parent_key, level):
        """获取或创建部位编码"""
        if pd.isna(part_name) or part_name == '':
            return '00'

        # 初始化编码映射缓存
        if not hasattr(self, '_part_code_cache'):
            self._part_code_cache = {}

        cache_key = f"{level}_{parent_key}_{part_name}"

        if cache_key in self._part_code_cache:
            return self._part_code_cache[cache_key]

        # 为该级别的部位生成顺序编码
        level_key = f"{level}_{parent_key}"
        if level_key not in self._part_code_cache:
            self._part_code_cache[level_key] = {}

        level_cache = self._part_code_cache[level_key]

        if part_name not in level_cache:
            # 生成新的顺序编码
            next_code = len(level_cache) + 1
            if next_code > 99:
                next_code = 99  # 最大两位数
            level_cache[part_name] = str(next_code).zfill(2)

        code = level_cache[part_name]
        self._part_code_cache[cache_key] = code
        return code

    def parse_position_from_name(self, project_name, position_name):
        """从项目名称和摆位名称中解析摆位信息"""
        if pd.isna(position_name):
            position_name = ""

        # 清理摆位名称，去除DR后缀，保留括号内容
        clean_position = str(position_name).replace('DR', '').strip()

        # 解析方向和体位编码
        direction_code = self._find_direction_code(clean_position)
        position_code = self._find_position_code(clean_position)

        # 生成2位摆位编码：体位编码 + 方向编码
        pose_code = f"{position_code}{direction_code}"

        return direction_code, position_code, clean_position, pose_code



    def _ensure_unique_code(self, base_code, existing_items):
        """确保项目编码的唯一性"""
        existing_codes = {item['项目编码'] for item in existing_items}

        if base_code not in existing_codes:
            return base_code

        # 如果基础编码已存在，添加序号后缀
        counter = 1
        while True:
            new_code = f"{base_code}_{counter:02d}"
            if new_code not in existing_codes:
                return new_code
            counter += 1

    def _format_project_name(self, part_name, position_name):
        """格式化项目名称：DR + 三级部位 + (摆位名称)"""
        if pd.isna(part_name) or part_name == '':
            part_name = '未知部位'

        if pd.isna(position_name) or position_name == '':
            return f"DR{part_name}"

        # 处理括号内容
        # 如果摆位名称中已经包含括号，保持原样
        if '（' in position_name or '(' in position_name:
            # 分离主要摆位名称和括号内容
            import re
            # 匹配中文括号或英文括号
            match = re.search(r'(.+?)[（(](.+?)[）)]', position_name)
            if match:
                main_position = match.group(1).strip()
                bracket_content = match.group(2).strip()
                return f"DR{part_name}({main_position})（{bracket_content}）"
            else:
                return f"DR{part_name}({position_name})"
        else:
            return f"DR{part_name}({position_name})"

    def _find_direction_code(self, position_text):
        """查找方向编码"""
        position_text = str(position_text).lower()

        # 精确匹配优先级列表（按优先级排序）
        direction_patterns = [
            # 特殊位置优先匹配
            ('汤氏位', '0'),
            ('颏顶位', '2'),
            ('顶颏位', '2'),
            ('梅氏位', '2'),
            ('劳氏位', '2'),
            ('伦氏位', '2'),
            ('许氏位', '2'),
            ('华氏位', '2'),
            ('柯氏位', '2'),
            ('斯氏位', '2'),
            ('反斯氏位', '2'),
            ('电子耳蜗位', '2'),
            ('耳蜗位', '2'),
            ('孔后前斜位', 'L'),
            ('后前斜位', '7'),
            ('左后前斜位', 'K'),
            ('右后前斜位', 'L'),
            ('左前后斜位', 'E'),
            ('右前后斜位', 'F'),
            ('髁间窝位', '2'),
            ('闭孔位', '2'),
            ('耻骨弓位', '2'),
            ('出口位', '2'),
            ('入口位', '2'),
            # 常规方向
            ('后前正位', '1'),
            ('前后正位', '0'),
            ('后前位', '1'),
            ('前后位', '0'),
            ('正位', '0'),
            ('左侧位', '8'),
            ('右侧位', '9'),
            ('侧位', 'A'),
            ('轴位', '2'),
            ('切线位', '5'),
            ('左斜位', 'K'),
            ('右斜位', 'L'),
            ('斜位', '6'),
            ('穿胸位', 'D')
        ]

        for pattern, code in direction_patterns:
            if pattern in position_text:
                return code

        return 'Z'  # 默认为"没有"

    def _find_position_code(self, position_text):
        """查找体位编码"""
        position_text = str(position_text).lower()

        # 精确匹配优先级列表（按优先级排序）
        position_patterns = [
            # 特殊体位优先匹配
            ('站立', '8'),
            ('倒立', '9'),
            ('左侧卧', 'A'),
            ('右侧卧', 'B'),
            ('仰卧', '0'),
            ('俯卧', '1'),
            ('张口', 'R'),
            ('闭口', 'C'),
            ('应力', 'E'),
            ('外展', 'M'),
            ('内收', 'N'),
            ('过伸', 'P'),
            ('过屈', 'Q'),
            ('蛙形', 'T'),
            ('前弓', 'S'),
            ('左前斜', '4'),
            ('右前斜', '5'),
            ('左后斜', '6'),
            ('右后斜', '7'),
            ('内斜', 'J'),
            ('外斜', 'K'),
            ('斜位', '3'),
            ('侧卧', '2')
        ]

        for pattern, code in position_patterns:
            if pattern in position_text:
                return code

        return 'Z'  # 默认为"没有"

    def generate_dr_items(self):
        """生成DR检查项目清单"""
        if self.dr_df is None:
            return None

        # 如果有部位映射结果，使用映射后的标准编码
        if self.part_mapping_results is not None:
            return self._generate_dr_items_with_standard_codes()
        else:
            # 使用原有逻辑（向后兼容）
            return self._generate_dr_items_legacy()

    def _generate_dr_items_with_standard_codes(self):
        """使用标准部位编码生成DR检查项目清单"""
        items = []
        seen_combinations = set()

        for _, mapping_row in self.part_mapping_results.iterrows():
            # 只处理映射成功的项目
            if mapping_row['映射状态'] not in ['成功', '模糊匹配']:
                continue

            project_name = mapping_row['DR项目名称']
            dr_row_idx = mapping_row['DR行号'] - 1  # 转换为0基索引

            # 获取原始DR数据行
            if dr_row_idx >= len(self.dr_df):
                continue

            dr_row = self.dr_df.iloc[dr_row_idx]
            position_name = dr_row.get('摆位', '')

            # 跳过无效数据
            if pd.isna(project_name) or project_name == '':
                continue

            # 创建唯一标识符来避免重复处理相同的组合
            combination_key = f"{project_name}|{position_name}"
            if combination_key in seen_combinations:
                continue
            seen_combinations.add(combination_key)

            # 使用标准部位编码
            part_code = mapping_row['标准部位编码']
            level1_code = mapping_row['标准一级编码']
            level1_part = mapping_row['标准一级部位']
            level2_code = mapping_row['标准二级编码']
            level2_part = mapping_row['标准二级部位']
            level3_code = mapping_row['标准三级编码']
            level3_part = mapping_row['标准三级部位']

            # 解析摆位信息
            direction_code, position_code, clean_position, pose_code = self.parse_position_from_name(
                project_name, position_name
            )

            # 生成项目编码：DR + 六位部位编码 + 二位摆位编码
            base_code = f"DR{part_code}{pose_code}"

            # 确保编码唯一性
            item_code = self._ensure_unique_code(base_code, items)

            # 生成标准化项目名称：DR + 三级部位 + (摆位名称)
            formatted_name = self._format_project_name(level3_part, clean_position)

            item = {
                '模态': 'DR',
                '一级编码': level1_code,
                '一级部位': level1_part,
                '二级编码': level2_code,
                '二级部位': level2_part,
                '三级编码': level3_code,
                '三级部位': level3_part,
                '项目编码': item_code,
                '项目名称': formatted_name,
                # 额外信息
                '部位编码': part_code,
                '摆位编码': pose_code,
                '方向编码': direction_code,
                '体位编码': position_code,
                '原始项目名称': project_name,
                '原始摆位': position_name,
                '清理后摆位': clean_position,
                '映射状态': mapping_row['映射状态'],
                'DR适用性': mapping_row['DR适用性']
            }

            items.append(item)

        return pd.DataFrame(items)

    def _generate_dr_items_legacy(self):
        """使用原有逻辑生成DR检查项目清单（向后兼容）"""
        items = []
        seen_combinations = set()  # 用于跟踪已处理的组合，避免重复

        for _, row in self.dr_df.iterrows():
            # 获取基本信息
            level1_part = row.get('一级部位', '')
            level2_part = row.get('二级部位', '')
            level3_part = row.get('三级部位', '')
            project_name = row.get('项目名称', '')
            position_name = row.get('摆位', '')

            # 跳过无效数据
            if pd.isna(project_name) or project_name == '':
                continue

            # 创建唯一标识符来避免重复处理相同的组合
            # 使用项目名称作为主要标识符，因为它更准确
            combination_key = f"{project_name}|{position_name}"
            if combination_key in seen_combinations:
                continue
            seen_combinations.add(combination_key)

            # 生成部位编码
            part_code = self.generate_part_code(level1_part, level2_part, level3_part)

            # 解析摆位信息
            direction_code, position_code, clean_position, pose_code = self.parse_position_from_name(
                project_name, position_name
            )

            # 生成项目编码：DR + 六位部位编码 + 二位摆位编码
            base_code = f"DR{part_code}{pose_code}"

            # 确保编码唯一性
            item_code = self._ensure_unique_code(base_code, items)

            # 生成标准化项目名称：DR + 三级部位 + (摆位名称)
            formatted_name = self._format_project_name(level3_part, clean_position)

            item = {
                '模态': 'DR',
                '一级编码': part_code[:2],
                '一级部位': level1_part,
                '二级编码': part_code[2:4],
                '二级部位': level2_part,
                '三级编码': part_code[4:6],
                '三级部位': level3_part,
                '项目编码': item_code,
                '项目名称': formatted_name,
                # 额外信息
                '部位编码': part_code,
                '摆位编码': pose_code,
                '方向编码': direction_code,
                '体位编码': position_code,
                '原始项目名称': project_name,
                '原始摆位': position_name,
                '清理后摆位': clean_position
            }

            items.append(item)

        return pd.DataFrame(items)

    def get_basic_stats(self):
        """获取基本统计信息"""
        if self.dr_df is None:
            return None

        stats = {
            'total_records': len(self.dr_df),
            'valid_projects': len(self.dr_df[self.dr_df['项目名称'].notna()]),
            'level1_parts': len(self.dr_df['一级部位'].unique()),
            'level2_parts': len(self.dr_df['二级部位'].unique()),
            'level3_parts': len(self.dr_df['三级部位'].unique()),
            'positions': len(self.dr_df['摆位'].unique()),
            'direction_codes': len(self.direction_mapping),
            'position_codes': len(self.position_mapping)
        }

        return stats

    def format_output_columns(self, items_df):
        """格式化输出列顺序为标准格式"""
        if items_df is None or len(items_df) == 0:
            return items_df

        # 标准输出列顺序
        standard_columns = [
            '模态',
            '一级编码',
            '一级部位',
            '二级编码',
            '二级部位',
            '三级编码',
            '三级部位',
            '项目编码',
            '项目名称'
        ]

        # 确保所有标准列都存在
        for col in standard_columns:
            if col not in items_df.columns:
                items_df[col] = ''

        # 返回按标准顺序排列的DataFrame
        return items_df[standard_columns]
