#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学检查项目处理流程演示脚本
展示核心功能的命令行版本
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import re

def get_project_paths():
    """获取项目路径配置"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 项目根目录（src的上级目录）
    project_root = os.path.dirname(current_dir)

    # 数据目录和输出目录
    data_dir = os.path.join(project_root, "data")
    output_dir = os.path.join(project_root, "output")

    return {
        'project_root': project_root,
        'data_dir': data_dir,
        'output_dir': output_dir,
        'default_data_file': os.path.join(data_dir, "NEW_检查项目名称结构表 (8).xlsx")
    }

class MedicalItemProcessorDemo:
    """医学检查项目处理器演示版"""

    def __init__(self, excel_file):
        self.excel_file = excel_file
        self.main_df = None
        self.ct_scan_df = None
        self.mr_scan_df = None

    def load_data(self):
        """加载Excel数据"""
        try:
            print("正在加载数据...")

            # 加载主数据表
            self.main_df = pd.read_excel(self.excel_file, sheet_name='三级部位结构')
            print(f"✓ 主数据表：{len(self.main_df)}行，{len(self.main_df.columns)}列")

            # 加载CT扫描方式表
            self.ct_scan_df = pd.read_excel(self.excel_file, sheet_name='CT扫描方式')
            print(f"✓ CT扫描方式：{len(self.ct_scan_df)}行")

            # 加载MR扫描方式表
            self.mr_scan_df = pd.read_excel(self.excel_file, sheet_name='MR扫描方式')
            print(f"✓ MR扫描方式：{len(self.mr_scan_df)}行")

            return True
        except Exception as e:
            print(f"✗ 数据加载失败：{e}")
            return False

    def analyze_data(self):
        """数据基本分析"""
        print("\n" + "="*50)
        print("数据分析结果")
        print("="*50)

        # 基本统计
        print(f"主数据表规模：{len(self.main_df)}行 × {len(self.main_df.columns)}列")
        print(f"缺失值总数：{self.main_df.isnull().sum().sum()}")

        # 一级部位分布
        print("\n一级部位分布：")
        part_counts = self.main_df['一级部位'].value_counts()
        for part, count in part_counts.head(5).items():
            print(f"  {part}: {count}项")

        # 模态分布
        ct_count = self.main_df[self.main_df['CT'].astype(str).str.strip().isin(['1', '１'])].shape[0]
        mr_count = self.main_df[self.main_df['MR'].astype(str).str.strip().isin(['1', '１'])].shape[0]
        print(f"\nCT适用部位：{ct_count}个")
        print(f"MR适用部位：{mr_count}个")

    def clean_scan_name(self, scan_name, modality):
        """清理扫描方式名称"""
        if pd.isna(scan_name):
            return ""

        scan_name = str(scan_name).strip()

        if modality == 'CT':
            scan_name = scan_name.replace('CT-', '').replace('CT_', '').replace('CT', '')
        elif modality == 'MR':
            scan_name = scan_name.replace('MR-', '').replace('MR_', '').replace('MR', '')

        return re.sub(r'\s+', ' ', scan_name).strip()

    def generate_three_level_dict(self):
        """生成三级部位字典表"""
        print("\n" + "="*50)
        print("生成三级部位字典表")
        print("="*50)

        dict_data = []
        for _, row in self.main_df.iterrows():
            part_code = str(row.get('部位编码', '')).strip()
            if len(part_code) == 6 and part_code.isdigit():
                dict_item = {
                    '一级编码': row.get('一级编码', ''),
                    '一级部位': str(row.get('一级部位', '')).strip(),
                    '二级编码': row.get('二级编码', ''),
                    '二级部位': str(row.get('二级部位', '')).strip(),  # 修复：使用正确的列名
                    '三级编码': row.get('三级编码', ''),
                    '三级部位': str(row.get('三级部位', '')).strip(),
                    '部位编码': part_code,
                    'CT适用': '是' if str(row.get('CT', '')).strip() in ['1', '１'] else '否',
                    'MR适用': '是' if str(row.get('MR', '')).strip() in ['1', '１'] else '否'
                }
                dict_data.append(dict_item)

        three_level_dict = pd.DataFrame(dict_data)
        print(f"✓ 成功生成三级部位字典表：{len(three_level_dict)}条记录")

        # 数据质量检查
        valid_codes = (three_level_dict['部位编码'].str.len() == 6).sum()
        duplicate_codes = three_level_dict['部位编码'].duplicated().sum()
        print(f"  有效编码：{valid_codes}个")
        print(f"  重复编码：{duplicate_codes}个")

        return three_level_dict

    def generate_scan_dict(self, modality):
        """生成扫描方式字典表 - 保留原始sheet表中的所有字段"""
        print(f"\n生成{modality}扫描方式字典表...")

        if modality == 'CT' and self.ct_scan_df is not None:
            # 复制原始数据框，保留所有字段
            scan_dict = self.ct_scan_df.copy()

            # 过滤掉无效数据
            scan_dict = scan_dict.dropna(subset=['CT扫描名称', 'CT扫描编码'])

            # 添加清理后名称字段
            scan_dict['清理后名称'] = scan_dict['CT扫描名称'].apply(
                lambda x: self.clean_scan_name(x, 'CT') if pd.notna(x) else ''
            )

            # 确保编码格式正确
            scan_dict['CT扫描编码'] = scan_dict['CT扫描编码'].apply(
                lambda x: str(x).zfill(2) if pd.notna(x) else ''
            )

            # 重置索引
            scan_dict = scan_dict.reset_index(drop=True)

            print(f"✓ CT扫描方式字典表：{len(scan_dict)}条记录，{len(scan_dict.columns)}个字段")
            print(f"  保留的字段：{list(scan_dict.columns)}")
            return scan_dict

        elif modality == 'MR' and self.mr_scan_df is not None:
            # 复制原始数据框，保留所有字段
            scan_dict = self.mr_scan_df.copy()

            # 过滤掉无效数据
            scan_dict = scan_dict.dropna(subset=['MR成像名称', 'MR成像编码'])

            # 添加清理后名称字段
            scan_dict['清理后名称'] = scan_dict['MR成像名称'].apply(
                lambda x: self.clean_scan_name(x, 'MR') if pd.notna(x) else ''
            )

            # 确保编码格式正确
            scan_dict['MR成像编码'] = scan_dict['MR成像编码'].apply(
                lambda x: str(x).zfill(2) if pd.notna(x) else ''
            )

            # 重置索引
            scan_dict = scan_dict.reset_index(drop=True)

            print(f"✓ MR扫描方式字典表：{len(scan_dict)}条记录，{len(scan_dict.columns)}个字段")
            print(f"  保留的字段：{list(scan_dict.columns)}")
            return scan_dict

        return None

    def generate_check_items(self, modality):
        """生成检查项目"""
        print(f"\n" + "="*50)
        print(f"生成{modality}检查项目清单")
        print("="*50)

        items = []

        # 创建扫描方式映射
        mapping = self.create_scan_mapping(modality)

        # 获取相关列
        if modality == 'CT':
            scan_columns = [col for col in self.main_df.columns if col.startswith('CT') and col != 'CT']
        else:
            scan_columns = [col for col in self.main_df.columns if col.startswith('MR') and col != 'MR']

        for _, row in self.main_df.iterrows():
            if pd.notna(row.get(modality)) and str(row.get(modality)).strip() in ['1', '１']:
                part_name = str(row['三级部位']).strip()
                part_code = str(row['部位编码']).strip()

                for scan_col in scan_columns:
                    if pd.notna(row[scan_col]) and row[scan_col] == 1.0:
                        scan_raw_name = scan_col
                        scan_clean_name = self.clean_scan_name(scan_raw_name, modality)
                        scan_code = self.find_scan_code(scan_clean_name, mapping, modality)

                        item_name = f"{modality}{part_name}({scan_clean_name})"
                        item_code = f"{modality}{part_code}{scan_code}".replace(' ', '')

                        item = {
                            '模态': modality,
                            '一级编码': row.get('一级编码', ''),
                            '一级部位': row['一级部位'],
                            '二级编码': row.get('二级编码', ''),
                            '二级部位': row.get('二级部位', ''),
                            '三级编码': row.get('三级编码', ''),
                            '三级部位': part_name,
                            '项目编码': item_code,
                            '项目名称': item_name,
                            # 保留原有列用于内部处理
                            '部位编码': part_code,
                            '扫描方式清理名': scan_clean_name,
                            '扫描编码': scan_code,
                            '检查模态': modality
                        }
                        items.append(item)

        items_df = pd.DataFrame(items)

        # 生成无部位项目并合并
        no_part_items = self.generate_no_part_items(modality)
        if no_part_items is not None and len(no_part_items) > 0:
            items_df = pd.concat([items_df, no_part_items], ignore_index=True)

        print(f"✓ 成功生成{modality}检查项目：{len(items_df)}个")
        if no_part_items is not None:
            print(f"  其中无部位项目：{len(no_part_items)}个")

        # 格式化输出列顺序
        formatted_df = self.format_output_columns(items_df)

        # 显示示例
        if len(formatted_df) > 0:
            print(f"\n{modality}项目示例（标准9列格式）：")
            for i, (_, row) in enumerate(formatted_df.head(3).iterrows()):
                print(f"  {row['项目编码']} - {row['项目名称']}")
                if row['一级部位'] == '无指定部位':
                    print(f"    [无部位项目] 二级部位: {row['二级部位']} | 三级部位: {row['三级部位']}")

        return formatted_df

    def generate_no_part_items(self, modality):
        """生成无部位项目"""
        items = []

        if modality == 'CT' and self.ct_scan_df is not None:
            for _, row in self.ct_scan_df.iterrows():
                # CT无部位项目生成规则
                scan_class_code = row.get('CT扫描分类编码', '')
                scan_code = row.get('CT扫描编码', '')
                scan_class_name = row.get('CT扫描分类名称', '')
                scan_name = row.get('CT扫描名称', '')

                # 检查数据有效性，跳过NaN值
                if (pd.notna(scan_class_code) and pd.notna(scan_code) and
                    pd.notna(scan_class_name) and pd.notna(scan_name) and
                    str(scan_class_code).strip() != '' and str(scan_code).strip() != '' and
                    str(scan_class_name).strip() != '' and str(scan_name).strip() != ''):

                    scan_class_code = str(scan_class_code).zfill(2)
                    scan_code = str(scan_code).zfill(2)
                    scan_class_name = str(scan_class_name).strip()
                    scan_name = str(scan_name).strip()
                    # 项目编码格式：CT + 09 + 二级编码 + 三级编码 + 00
                    item_code = f"CT09{scan_class_code}{scan_code}00"

                    item = {
                        '模态': 'CT',
                        '一级编码': '09',
                        '一级部位': '无指定部位',
                        '二级编码': scan_class_code,
                        '二级部位': scan_class_name,
                        '三级编码': scan_code,
                        '三级部位': scan_name,
                        '项目编码': item_code,
                        '项目名称': scan_name,
                        # 保留原有列用于内部处理
                        '部位编码': '090000',  # 无部位的特殊编码
                        '扫描方式清理名': '',
                        '扫描编码': '00',
                        '检查模态': 'CT',
                        # 保留扫描方式相关字段的完整性
                        'CT扫描分类编码': scan_class_code,
                        'CT扫描分类名称': scan_class_name,
                        'CT扫描编码': scan_code,
                        'CT扫描名称': scan_name
                    }
                    items.append(item)

        elif modality == 'MR' and self.mr_scan_df is not None:
            for _, row in self.mr_scan_df.iterrows():
                # MR无部位项目生成规则
                imaging_class_code = row.get('MR成像分类编码', '')
                imaging_code = row.get('MR成像编码', '')
                imaging_class_name = row.get('MR成像分类', '')
                imaging_name = row.get('MR成像名称', '')

                # 检查数据有效性，跳过NaN值
                if (pd.notna(imaging_class_code) and pd.notna(imaging_code) and
                    pd.notna(imaging_class_name) and pd.notna(imaging_name) and
                    str(imaging_class_code).strip() != '' and str(imaging_code).strip() != '' and
                    str(imaging_class_name).strip() != '' and str(imaging_name).strip() != ''):

                    imaging_class_code = str(imaging_class_code).zfill(2)
                    imaging_code = str(imaging_code).zfill(2)
                    imaging_class_name = str(imaging_class_name).strip()
                    imaging_name = str(imaging_name).strip()
                    # 项目编码格式：MR + 09 + 二级编码 + 三级编码 + 00
                    item_code = f"MR09{imaging_class_code}{imaging_code}00"

                    item = {
                        '模态': 'MR',
                        '一级编码': '09',
                        '一级部位': '无指定部位',
                        '二级编码': imaging_class_code,
                        '二级部位': imaging_class_name,
                        '三级编码': imaging_code,
                        '三级部位': imaging_name,
                        '项目编码': item_code,
                        '项目名称': imaging_name,
                        # 保留原有列用于内部处理
                        '部位编码': '090000',  # 无部位的特殊编码
                        '扫描方式清理名': '',
                        '扫描编码': '00',
                        '检查模态': 'MR',
                        # 保留扫描方式相关字段的完整性
                        'MR成像分类编码': imaging_class_code,
                        'MR成像分类': imaging_class_name,
                        'MR成像编码': imaging_code,
                        'MR成像名称': imaging_name
                    }
                    items.append(item)

        return pd.DataFrame(items) if items else None

    def format_output_columns(self, items_df):
        """格式化输出列顺序为标准格式"""
        if items_df is None or len(items_df) == 0:
            return items_df

        # 标准输出列顺序
        standard_columns = [
            '模态',
            '一级编码',
            '一级部位',
            '二级编码',
            '二级部位',
            '三级编码',
            '三级部位',
            '项目编码',
            '项目名称'
        ]

        # 确保所有标准列都存在
        for col in standard_columns:
            if col not in items_df.columns:
                items_df[col] = ''

        # 返回按标准顺序排列的DataFrame
        return items_df[standard_columns]

    def create_scan_mapping(self, modality):
        """创建扫描方式映射表"""
        mapping = {}

        if modality == 'CT' and self.ct_scan_df is not None:
            for _, row in self.ct_scan_df.iterrows():
                scan_name = row.get('CT扫描名称', '')
                scan_code = row.get('CT扫描编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    mapping[scan_name] = str(scan_code).zfill(2)

        elif modality == 'MR' and self.mr_scan_df is not None:
            for _, row in self.mr_scan_df.iterrows():
                scan_name = row.get('MR成像名称', '')
                scan_code = row.get('MR成像编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    mapping[scan_name] = str(scan_code).zfill(2)

        return mapping

    def find_scan_code(self, scan_clean_name, mapping, modality):
        """查找扫描编码"""
        # 直接匹配
        for key, code in mapping.items():
            if scan_clean_name in key or key in scan_clean_name:
                return code

        # 默认编码规则 - 根据实际数据表修正血管造影类扫描方式编码
        if modality == 'CT':
            default_codes = {
                '平扫': '10', '增强': '20', 'CTA': '41', 'CTV': '42', '灌注': '50'
            }
        else:
            # MR默认编码 - 按照实际数据表的编码
            default_codes = {
                '平扫': '10', '增强': '20',
                'MRA+CE_MRA': '61', 'MRV+CE_MRV': '62',
                'CE_MRA': '51', 'CE_MRV': '52',
                'MRA': '41', 'MRV': '42'
            }

        for key, code in default_codes.items():
            if key in scan_clean_name:
                return code

        return '99'

    def quality_control(self, ct_items, mr_items):
        """质量控制分析"""
        print("\n" + "="*50)
        print("数据质量控制分析")
        print("="*50)

        # 综合统计
        print(f"CT项目总数：{len(ct_items)}")
        print(f"MR项目总数：{len(mr_items)}")
        print(f"总项目数：{len(ct_items) + len(mr_items)}")

        # 错误检查
        errors = []

        # 检查编码格式
        if len(ct_items) > 0:
            invalid_ct = ct_items[ct_items['项目编码'].str.len() != 10]
            if len(invalid_ct) > 0:
                errors.append(f"CT编码格式错误：{len(invalid_ct)}项")

        if len(mr_items) > 0:
            invalid_mr = mr_items[mr_items['项目编码'].str.len() != 10]
            if len(invalid_mr) > 0:
                errors.append(f"MR编码格式错误：{len(invalid_mr)}项")

        # 检查重复编码
        all_codes = []
        if len(ct_items) > 0:
            all_codes.extend(ct_items['项目编码'].tolist())
        if len(mr_items) > 0:
            all_codes.extend(mr_items['项目编码'].tolist())

        duplicate_codes = len(all_codes) - len(set(all_codes))
        if duplicate_codes > 0:
            errors.append(f"重复编码：{duplicate_codes}项")

        if errors:
            print("\n发现的问题：")
            for error in errors:
                print(f"  ✗ {error}")
        else:
            print("\n✓ 未发现数据质量问题")

    def export_results(self, ct_items, mr_items, three_level_dict):
        """导出结果"""
        print("\n" + "="*50)
        print("导出处理结果")
        print("="*50)

        # 获取路径配置
        paths = get_project_paths()
        output_dir = paths['output_dir']

        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✓ 创建输出目录：{output_dir}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"医学检查项目处理结果_{timestamp}.xlsx")

        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 检查项目清单（使用标准9列格式）
                ct_items.to_excel(writer, sheet_name='CT检查项目清单', index=False)
                mr_items.to_excel(writer, sheet_name='MR检查项目清单', index=False)

                # 三级部位字典
                three_level_dict.to_excel(writer, sheet_name='三级部位字典', index=False)

                # 扫描方式字典
                ct_scan_dict = self.generate_scan_dict('CT')
                mr_scan_dict = self.generate_scan_dict('MR')
                if ct_scan_dict is not None:
                    ct_scan_dict.to_excel(writer, sheet_name='CT扫描方式字典', index=False)
                if mr_scan_dict is not None:
                    mr_scan_dict.to_excel(writer, sheet_name='MR扫描方式字典', index=False)

                # 添加列格式说明工作表
                format_info = pd.DataFrame({
                    '列序号': [1, 2, 3, 4, 5, 6, 7, 8, 9],
                    '列名称': ['模态', '一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目编码', '项目名称'],
                    '数据类型': ['文本', '数字', '文本', '数字', '文本', '数字', '文本', '文本', '文本'],
                    '说明': [
                        'CT或MR',
                        '一级部位编码',
                        '一级部位名称',
                        '二级部位编码',
                        '二级部位名称',
                        '三级部位编码',
                        '三级部位名称',
                        '10位项目编码',
                        '标准格式项目名称'
                    ],
                    '示例': [
                        'CT',
                        '01',
                        '头部',
                        '01',
                        '颅脑',
                        '01',
                        '颅脑',
                        'CT01010110',
                        'CT颅脑(平扫)'
                    ]
                })
                format_info.to_excel(writer, sheet_name='列格式说明', index=False)

            print(f"✓ 结果已导出到：{output_file}")
            print(f"✓ Excel文件包含标准9列格式的CT和MR检查项目清单")
            return output_file

        except Exception as e:
            print(f"✗ 导出失败：{e}")
            return None

def main():
    """主函数"""
    print("🏥 医学检查项目名称和编码处理流程演示")
    print("="*60)

    # 获取路径配置
    paths = get_project_paths()
    excel_file = paths['default_data_file']

    # 检查数据文件
    if not os.path.exists(excel_file):
        print(f"✗ 找不到数据文件：{excel_file}")
        print("请确保数据文件位于 data/ 目录下")
        return

    # 创建处理器
    processor = MedicalItemProcessorDemo(excel_file)

    # 步骤1：加载数据
    if not processor.load_data():
        return

    # 步骤2：数据分析
    processor.analyze_data()

    # 步骤3：生成三级部位字典表
    three_level_dict = processor.generate_three_level_dict()

    # 步骤4：生成扫描方式字典表
    ct_scan_dict = processor.generate_scan_dict('CT')
    mr_scan_dict = processor.generate_scan_dict('MR')

    # 步骤5：生成检查项目清单
    ct_items = processor.generate_check_items('CT')
    mr_items = processor.generate_check_items('MR')

    # 步骤6：质量控制
    processor.quality_control(ct_items, mr_items)

    # 步骤7：导出结果
    output_file = processor.export_results(ct_items, mr_items, three_level_dict)

    print("\n" + "="*60)
    print("🎉 处理流程完成！")
    if output_file:
        print(f"📄 结果文件：{output_file}")
    print("💻 要使用交互式界面，请运行：streamlit run streamlit_medical_pipeline.py")

if __name__ == "__main__":
    main()
