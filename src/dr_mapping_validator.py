#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR部位映射验证器
独立的DR部位映射处理和验证模块
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import re


class DRMappingValidator:
    """DR部位映射验证器"""

    def __init__(self):
        self.dr_df = None
        self.three_level_dict = None
        self.mapping_results = None
        self.difference_analysis = None

    def load_dr_data(self, dr_df: pd.DataFrame) -> bool:
        """加载DR数据"""
        try:
            self.dr_df = dr_df.copy()
            return True
        except Exception as e:
            print(f"DR数据加载失败: {e}")
            return False

    def load_standard_dict(self, three_level_dict: pd.DataFrame) -> bool:
        """加载标准三级部位字典"""
        try:
            self.three_level_dict = three_level_dict.copy()
            return True
        except Exception as e:
            print(f"标准字典加载失败: {e}")
            return False

    def perform_bidirectional_mapping(self) -> <PERSON><PERSON>[bool, str]:
        """执行双向映射验证"""
        if self.dr_df is None or self.three_level_dict is None:
            return False, "数据未加载完成"

        try:
            # 1. DR到标准字典的映射
            dr_to_standard = self._map_dr_to_standard()

            # 2. 标准字典到DR的映射
            standard_to_dr = self._map_standard_to_dr()

            # 3. 差异分析
            self.difference_analysis = self._analyze_differences(dr_to_standard, standard_to_dr)

            # 4. 生成映射结果
            self.mapping_results = dr_to_standard

            return True, "双向映射验证完成"

        except Exception as e:
            return False, f"映射验证失败: {str(e)}"

    def _map_dr_to_standard(self) -> pd.DataFrame:
        """DR数据映射到标准字典"""
        mapping_results = []

        # 创建标准字典查找索引
        standard_lookup = self._create_standard_lookup()

        # 处理每个DR数据行（而不是唯一部位组合）
        for idx, row in self.dr_df.iterrows():
            project_name = str(row.get('项目名称', '')).strip()

            # 跳过无效数据
            if pd.isna(project_name) or project_name == '' or project_name == 'nan':
                continue

            dr_level1 = str(row.get('一级部位', '')).strip()
            dr_level2 = str(row.get('二级部位', '')).strip()
            dr_level3 = str(row.get('三级部位', '')).strip()

            # 跳过无效数据
            if not dr_level1 or not dr_level2 or not dr_level3:
                continue

            # 构建查找键
            lookup_key = f"{dr_level1}|{dr_level2}|{dr_level3}"

            mapping_result = {
                'DR行号': idx + 1,
                'DR项目名称': project_name,
                'DR一级部位': dr_level1,
                'DR二级部位': dr_level2,
                'DR三级部位': dr_level3,
                '映射状态': '',
                '标准一级编码': '',
                '标准一级部位': '',
                '标准二级编码': '',
                '标准二级部位': '',
                '标准三级编码': '',
                '标准三级部位': '',
                '标准部位编码': '',
                'DR适用性': '',
                '映射类型': '',
                '相似度': 0.0,
                '错误信息': ''
            }

            # 尝试精确匹配
            if lookup_key in standard_lookup:
                std_part = standard_lookup[lookup_key]
                mapping_result.update({
                    '映射状态': '成功',
                    '标准一级编码': std_part['一级编码'],
                    '标准一级部位': std_part['一级部位'],
                    '标准二级编码': std_part['二级编码'],
                    '标准二级部位': std_part['二级部位'],
                    '标准三级编码': std_part['三级编码'],
                    '标准三级部位': std_part['三级部位'],
                    '标准部位编码': std_part['部位编码'],
                    'DR适用性': std_part['DR适用'],
                    '映射类型': '精确匹配',
                    '相似度': 1.0
                })

                # 检查DR适用性
                if std_part['DR适用'] != '是':
                    mapping_result['错误信息'] = '警告：该部位在标准字典中标记为不适用DR检查'
            else:
                # 尝试模糊匹配
                fuzzy_match = self._fuzzy_match_part(dr_level1, dr_level2, dr_level3, standard_lookup)
                if fuzzy_match:
                    mapping_result.update({
                        '映射状态': '模糊匹配',
                        '标准一级编码': fuzzy_match['match']['一级编码'],
                        '标准一级部位': fuzzy_match['match']['一级部位'],
                        '标准二级编码': fuzzy_match['match']['二级编码'],
                        '标准二级部位': fuzzy_match['match']['二级部位'],
                        '标准三级编码': fuzzy_match['match']['三级编码'],
                        '标准三级部位': fuzzy_match['match']['三级部位'],
                        '标准部位编码': fuzzy_match['match']['部位编码'],
                        'DR适用性': fuzzy_match['match']['DR适用'],
                        '映射类型': '模糊匹配',
                        '相似度': fuzzy_match['similarity'],
                        '错误信息': f'模糊匹配到：{fuzzy_match["match"]["一级部位"]}-{fuzzy_match["match"]["二级部位"]}-{fuzzy_match["match"]["三级部位"]}'
                    })
                else:
                    mapping_result.update({
                        '映射状态': '失败',
                        '映射类型': '无匹配',
                        '错误信息': f'未找到匹配的标准部位：{dr_level1}-{dr_level2}-{dr_level3}'
                    })

            mapping_results.append(mapping_result)

        return pd.DataFrame(mapping_results)

    def _map_standard_to_dr(self) -> pd.DataFrame:
        """标准字典映射到DR数据"""
        mapping_results = []

        # 获取标准字典中DR适用的部位
        dr_applicable_parts = self.three_level_dict[
            self.three_level_dict['DR适用'] == '是'
        ].copy()

        # 获取DR数据中的部位组合
        dr_parts_set = set()
        for _, row in self.dr_df.iterrows():
            if pd.notna(row.get('项目名称', '')):
                dr_level1 = str(row.get('一级部位', '')).strip()
                dr_level2 = str(row.get('二级部位', '')).strip()
                dr_level3 = str(row.get('三级部位', '')).strip()
                if dr_level1 and dr_level2 and dr_level3:
                    dr_parts_set.add(f"{dr_level1}|{dr_level2}|{dr_level3}")

        for _, std_part in dr_applicable_parts.iterrows():
            std_level1 = str(std_part.get('一级部位', '')).strip()
            std_level2 = str(std_part.get('二级部位', '')).strip()
            std_level3 = str(std_part.get('三级部位', '')).strip()

            lookup_key = f"{std_level1}|{std_level2}|{std_level3}"

            mapping_result = {
                '标准一级编码': std_part.get('一级编码', ''),
                '标准一级部位': std_level1,
                '标准二级编码': std_part.get('二级编码', ''),
                '标准二级部位': std_level2,
                '标准三级编码': std_part.get('三级编码', ''),
                '标准三级部位': std_level3,
                '标准部位编码': std_part.get('部位编码', ''),
                'DR适用性': std_part.get('DR适用', ''),
                '在DR数据中存在': '是' if lookup_key in dr_parts_set else '否',
                '缺失原因': '' if lookup_key in dr_parts_set else '标准字典中标记为DR适用，但DR数据中缺失'
            }

            mapping_results.append(mapping_result)

        return pd.DataFrame(mapping_results)

    def _analyze_differences(self, dr_to_standard: pd.DataFrame, standard_to_dr: pd.DataFrame) -> Dict:
        """分析映射差异"""
        analysis = {
            'dr_to_standard_stats': {
                '总DR部位数': len(dr_to_standard),
                '映射成功': len(dr_to_standard[dr_to_standard['映射状态'] == '成功']),
                '模糊匹配': len(dr_to_standard[dr_to_standard['映射状态'] == '模糊匹配']),
                '映射失败': len(dr_to_standard[dr_to_standard['映射状态'] == '失败']),
                '成功率': len(dr_to_standard[dr_to_standard['映射状态'].isin(['成功', '模糊匹配'])]) / len(dr_to_standard) * 100 if len(dr_to_standard) > 0 else 0
            },
            'standard_to_dr_stats': {
                '标准DR适用部位数': len(standard_to_dr),
                'DR数据中存在': len(standard_to_dr[standard_to_dr['在DR数据中存在'] == '是']),
                'DR数据中缺失': len(standard_to_dr[standard_to_dr['在DR数据中存在'] == '否']),
                '覆盖率': len(standard_to_dr[standard_to_dr['在DR数据中存在'] == '是']) / len(standard_to_dr) * 100 if len(standard_to_dr) > 0 else 0
            },
            'missing_in_standard': dr_to_standard[dr_to_standard['映射状态'] == '失败'].copy(),
            'missing_in_dr': standard_to_dr[standard_to_dr['在DR数据中存在'] == '否'].copy(),
            'fuzzy_matches': dr_to_standard[dr_to_standard['映射状态'] == '模糊匹配'].copy(),
            'dr_not_applicable': dr_to_standard[
                (dr_to_standard['映射状态'].isin(['成功', '模糊匹配'])) &
                (dr_to_standard['DR适用性'] != '是')
            ].copy()
        }

        return analysis

    def _create_standard_lookup(self) -> Dict:
        """创建标准字典查找索引"""
        lookup = {}
        for _, row in self.three_level_dict.iterrows():
            key = f"{row['一级部位']}|{row['二级部位']}|{row['三级部位']}"
            lookup[key] = {
                '一级编码': row['一级编码'],
                '一级部位': row['一级部位'],
                '二级编码': row['二级编码'],
                '二级部位': row['二级部位'],
                '三级编码': row['三级编码'],
                '三级部位': row['三级部位'],
                '部位编码': row['部位编码'],
                'DR适用': row.get('DR适用', '否')
            }
        return lookup

    def _get_unique_dr_parts(self) -> pd.DataFrame:
        """获取DR数据中的唯一部位组合"""
        # 过滤有效数据
        valid_dr = self.dr_df[self.dr_df['项目名称'].notna()].copy()

        # 按部位分组统计
        part_groups = valid_dr.groupby(['一级部位', '二级部位', '三级部位']).size().reset_index(name='项目数量')

        return part_groups

    def _fuzzy_match_part(self, dr_level1: str, dr_level2: str, dr_level3: str, standard_lookup: Dict) -> Optional[Dict]:
        """模糊匹配部位"""
        best_match = None
        best_similarity = 0.0

        for key, std_part in standard_lookup.items():
            std_level1, std_level2, std_level3 = key.split('|')

            # 计算相似度
            similarity = self._calculate_similarity(
                dr_level1, dr_level2, dr_level3,
                std_level1, std_level2, std_level3
            )

            if similarity > best_similarity and similarity >= 0.6:  # 相似度阈值
                best_similarity = similarity
                best_match = std_part

        if best_match:
            return {
                'match': best_match,
                'similarity': best_similarity
            }

        return None

    def _calculate_similarity(self, dr1: str, dr2: str, dr3: str, std1: str, std2: str, std3: str) -> float:
        """计算部位相似度"""
        # 简单的相似度计算逻辑
        scores = []

        # 一级部位匹配（权重最高）
        if dr1 == std1:
            scores.append(0.5)
        elif dr1 in std1 or std1 in dr1:
            scores.append(0.3)
        else:
            scores.append(0.0)

        # 二级部位匹配
        if dr2 == std2:
            scores.append(0.3)
        elif dr2 in std2 or std2 in dr2:
            scores.append(0.2)
        else:
            scores.append(0.0)

        # 三级部位匹配
        if dr3 == std3:
            scores.append(0.2)
        elif dr3 in std3 or std3 in dr3:
            scores.append(0.15)
        else:
            scores.append(0.0)

        return sum(scores)

    def get_mapping_statistics(self) -> Dict:
        """获取映射统计信息"""
        if self.difference_analysis is None:
            return {}

        return {
            'dr_to_standard': self.difference_analysis['dr_to_standard_stats'],
            'standard_to_dr': self.difference_analysis['standard_to_dr_stats']
        }

    def get_difference_details(self) -> Dict:
        """获取差异详情"""
        if self.difference_analysis is None:
            return {}

        return {
            'missing_in_standard': self.difference_analysis['missing_in_standard'],
            'missing_in_dr': self.difference_analysis['missing_in_dr'],
            'fuzzy_matches': self.difference_analysis['fuzzy_matches'],
            'dr_not_applicable': self.difference_analysis['dr_not_applicable']
        }

    def export_difference_report(self) -> pd.DataFrame:
        """导出差异分析报告"""
        if self.difference_analysis is None:
            return pd.DataFrame()

        # 创建综合报告
        report_data = []

        # 添加映射失败的DR部位
        for _, row in self.difference_analysis['missing_in_standard'].iterrows():
            report_data.append({
                '问题类型': 'DR部位缺失',
                '部位信息': f"{row['DR一级部位']}-{row['DR二级部位']}-{row['DR三级部位']}",
                '项目数量': 1,  # 每行代表一个项目
                '问题描述': '该DR部位在标准字典中不存在',
                '建议处理': '需要在标准字典中添加该部位或修正DR数据中的部位名称'
            })

        # 添加标准字典中缺失的DR部位
        for _, row in self.difference_analysis['missing_in_dr'].iterrows():
            report_data.append({
                '问题类型': '标准部位缺失',
                '部位信息': f"{row['标准一级部位']}-{row['标准二级部位']}-{row['标准三级部位']}",
                '项目数量': 0,
                '问题描述': '标准字典中标记为DR适用，但DR数据中缺失',
                '建议处理': '需要在DR数据中添加相应的检查项目或修正标准字典的DR适用性标记'
            })

        # 添加模糊匹配的部位
        for _, row in self.difference_analysis['fuzzy_matches'].iterrows():
            report_data.append({
                '问题类型': '模糊匹配',
                '部位信息': f"{row['DR一级部位']}-{row['DR二级部位']}-{row['DR三级部位']}",
                '项目数量': 1,  # 每行代表一个项目
                '问题描述': f"模糊匹配到：{row['标准一级部位']}-{row['标准二级部位']}-{row['标准三级部位']}（相似度：{row['相似度']:.2f}）",
                '建议处理': '建议人工确认映射关系是否正确'
            })

        return pd.DataFrame(report_data)
