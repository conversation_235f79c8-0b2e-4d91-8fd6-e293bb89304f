#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def verify_cleaning_effect():
    """验证清理效果"""

    # 读取结果文件
    result_file = 'output/DR完整匹配结果_20250706_050758.xlsx'
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')

    print("=== 清理效果验证 ===")

    # 获取有项目的记录
    has_projects = df[df['项目名称'] != '']
    print(f"有项目名称的记录数: {len(has_projects)}")

    print(f"\n1. 摆位中DR字符清理效果检查:")
    print("原始摆位 -> 项目名称中的摆位部分")

    # 检查前20个记录的清理效果
    for i, (idx, row) in enumerate(has_projects.head(20).iterrows()):
        original_position = row['摆位']
        project_name = row['项目名称']

        # 从项目名称中提取摆位部分（"-"后面的部分）
        if '-' in project_name:
            cleaned_position = project_name.split('-', 1)[1]
        else:
            cleaned_position = "无摆位"

        # 检查是否还包含DR
        has_dr = 'DR' in cleaned_position or 'dr' in cleaned_position
        status = "✗" if has_dr else "✓"

        print(f"{i+1:2d}. {status} {original_position:<15} -> {cleaned_position}")

    print(f"\n2. 部位名称空格清理效果检查:")
    print("检查项目名称中的部位部分是否还有空格")

    # 检查部位名称中的空格
    space_issues = []
    for idx, row in has_projects.iterrows():
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''
        part_name = str(row['三级部位']) if pd.notna(row['三级部位']) else ''

        # 从项目名称中提取部位部分（"DR"后面到"-"之间的部分）
        if project_name.startswith('DR') and '-' in project_name:
            extracted_part = project_name[2:project_name.index('-')]
            if ' ' in extracted_part:
                space_issues.append({
                    '原始部位': part_name,
                    '项目名称': project_name,
                    '提取部位': extracted_part
                })

    if space_issues:
        print(f"发现 {len(space_issues)} 个部位名称仍有空格:")
        for issue in space_issues[:10]:
            print(f"  - 原始: '{issue['原始部位']}' -> 项目: {issue['项目名称']}")
    else:
        print("✓ 所有部位名称的空格都已清理")

    print(f"\n3. 清理前后对比示例:")
    print("显示一些典型的清理效果")

    # 显示一些特殊情况的清理效果
    special_cases = []
    for idx, row in has_projects.iterrows():
        original_position = str(row['摆位']) if pd.notna(row['摆位']) else ''
        project_name = str(row['项目名称']) if pd.notna(row['项目名称']) else ''

        # 查找包含特殊字符或长名称的情况
        if original_position and (any(char in original_position for char in ['（', '）', 'DR']) or len(original_position) > 10):
            if '-' in project_name:
                cleaned_position = project_name.split('-', 1)[1]
                special_cases.append({
                    '原始摆位': original_position,
                    '清理后摆位': cleaned_position,
                    '项目名称': project_name
                })

    print("特殊情况清理示例:")
    for case in special_cases[:15]:
        print(f"  原始: {case['原始摆位']:<20} -> 清理: {case['清理后摆位']}")

    print(f"\n4. 统计信息:")

    # 统计包含DR的原始摆位
    original_with_dr = sum(1 for _, row in has_projects.iterrows() if 'DR' in str(row['摆位']))

    # 统计清理后仍包含DR的项目名称
    cleaned_with_dr = sum(1 for _, row in has_projects.iterrows()
                         if pd.notna(row['项目名称']) and '-' in str(row['项目名称']) and 'DR' in str(row['项目名称']).split('-', 1)[1])

    print(f"原始摆位包含DR的记录: {original_with_dr}")
    print(f"清理后仍包含DR的记录: {cleaned_with_dr}")
    print(f"DR清理成功率: {(original_with_dr - cleaned_with_dr) / original_with_dr * 100:.1f}%" if original_with_dr > 0 else "100%")

if __name__ == "__main__":
    verify_cleaning_effect()
