#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建最终的DR部位匹配结果，按照标准格式输出
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def create_final_formatted_output():
    """创建最终格式化的输出"""
    
    # 读取匹配结果
    result_file = 'output/DR部位匹配结果_20250706_034241.xlsx'
    df = pd.read_excel(result_file, sheet_name='匹配结果')
    
    print(f"原始数据行数: {len(df)}")
    
    # 移除空行分隔符
    df_clean = df[df['匹配状态'] != '空行分隔'].copy()
    print(f"清理后数据行数: {len(df_clean)}")
    
    # 创建最终输出格式
    final_output = []
    
    for idx, row in df_clean.iterrows():
        # 基本信息
        output_row = {
            'DR三级部位': row['三级部位'],
            'DR项目名称': row['项目名称'],
            '一级编码': row['一级编码'] if pd.notna(row['一级编码']) else '',
            '一级部位': row['一级部位'] if pd.notna(row['一级部位']) else '',
            '二级编码': row['二级编码'] if pd.notna(row['二级编码']) else '',
            '二级部位': row['二级部位'] if pd.notna(row['二级部位']) else '',
            '三级编码': row['三级编码'] if pd.notna(row['三级编码']) else '',
            '三级部位': row['三级部位'] if pd.notna(row['三级部位']) else '',
            '部位编码': row['部位编码'] if pd.notna(row['部位编码']) else '',
            '匹配状态': row['匹配状态']
        }
        
        final_output.append(output_row)
        
        # 如果是未匹配的记录，在后面插入空行
        if row['匹配状态'] == '未匹配':
            empty_row = {key: '' for key in output_row.keys()}
            empty_row['匹配状态'] = '--- 空行分隔 ---'
            final_output.append(empty_row)
    
    # 转换为DataFrame
    final_df = pd.DataFrame(final_output)
    
    # 统计信息
    matched_count = len(df_clean[df_clean['匹配状态'] == '已匹配'])
    unmatched_count = len(df_clean[df_clean['匹配状态'] == '未匹配'])
    
    print(f"\n数据统计:")
    print(f"已匹配记录: {matched_count}")
    print(f"未匹配记录: {unmatched_count}")
    print(f"总记录数: {len(df_clean)}")
    print(f"最终输出行数（含空行）: {len(final_df)}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"output/DR三级部位匹配最终结果_{timestamp}.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主结果sheet
        final_df.to_excel(writer, sheet_name='匹配结果', index=False)
        
        # 仅未匹配记录sheet
        unmatched_only = df_clean[df_clean['匹配状态'] == '未匹配'][['三级部位', '项目名称']].copy()
        unmatched_only.to_excel(writer, sheet_name='未匹配记录', index=False)
        
        # 统计信息sheet
        stats_data = {
            '统计项目': ['已匹配记录数', '未匹配记录数', '总记录数', '匹配率'],
            '数值': [matched_count, unmatched_count, len(df_clean), f"{matched_count/len(df_clean)*100:.1f}%"]
        }
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计信息', index=False)
    
    print(f"\n最终结果已保存到: {output_file}")
    
    # 显示未匹配的三级部位
    unmatched_parts = df_clean[df_clean['匹配状态'] == '未匹配']['三级部位'].unique()
    print(f"\n未匹配的三级部位列表 ({len(unmatched_parts)}个):")
    for i, part in enumerate(sorted(unmatched_parts), 1):
        print(f"{i:2d}. {part}")
    
    return output_file

if __name__ == "__main__":
    create_final_formatted_output()
