#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# 读取完整匹配结果文件
result_file = 'output/DR完整匹配结果_20250706_044506.xlsx'

try:
    # 读取完整匹配结果
    df = pd.read_excel(result_file, sheet_name='完整匹配结果')
    print(f"完整匹配结果:")
    print(f"总行数: {len(df)}")
    print(f"列数: {len(df.columns)}")

    # 检查列顺序
    print(f"\n列顺序检查:")
    expected_columns = ['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目名称', '摆位', 'DR检查项目编码']
    for i, col in enumerate(expected_columns):
        actual_col = df.columns[i] if i < len(df.columns) else "缺失"
        status = "✓" if actual_col == col else "✗"
        print(f"{i+1:2d}. {status} 期望: {col:<12} 实际: {actual_col}")

    # 检查编码格式
    print(f"\n编码格式检查:")
    print(f"一级编码格式: {df['一级编码'].dtype}, 示例: '{df['一级编码'].iloc[0]}'")
    print(f"二级编码格式: {df['二级编码'].dtype}, 示例: '{df['二级编码'].iloc[0]}'")
    print(f"三级编码格式: {df['三级编码'].dtype}, 示例: '{df['三级编码'].iloc[0]}'")

    # 转换为字符串后检查编码长度
    df_str = df.copy()
    for col in ['一级编码', '二级编码', '三级编码']:
        df_str[col] = df_str[col].astype(str).str.zfill(2)

    print(f"一级编码长度: {df_str['一级编码'].str.len().value_counts().to_dict()}")
    print(f"二级编码长度: {df_str['二级编码'].str.len().value_counts().to_dict()}")
    print(f"三级编码长度: {df_str['三级编码'].str.len().value_counts().to_dict()}")
    print(f"一级编码示例: {df_str['一级编码'].head(3).tolist()}")
    print(f"二级编码示例: {df_str['二级编码'].head(3).tolist()}")
    print(f"三级编码示例: {df_str['三级编码'].head(3).tolist()}")

    # 检查DR检查项目编码格式
    dr_codes = df[df['DR检查项目编码'] != '']['DR检查项目编码']
    if len(dr_codes) > 0:
        print(f"DR检查项目编码长度: {dr_codes.str.len().value_counts().to_dict()}")
        print(f"DR检查项目编码示例: {dr_codes.head().tolist()}")

    # 统计有项目和无项目的部位
    has_projects = df[df['项目名称'] != '']
    no_projects = df[df['项目名称'] == '']

    print(f"\n项目统计:")
    print(f"有DR项目的记录: {len(has_projects)}")
    print(f"无DR项目的记录: {len(no_projects)}")

    # 检查部位覆盖情况
    unique_parts = df.groupby(['一级编码', '二级编码', '三级编码', '三级部位']).size().reset_index(name='记录数')
    print(f"\n部位覆盖情况:")
    print(f"唯一三级部位数: {len(unique_parts)}")

    # 显示无项目的部位
    if len(no_projects) > 0:
        print(f"\n无DR项目的部位:")
        no_project_parts = no_projects[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位']].drop_duplicates()
        print(no_project_parts.head(10))

    # 读取统计信息
    stats_df = pd.read_excel(result_file, sheet_name='统计信息')
    print(f"\n统计信息:")
    print(stats_df)

    # 读取部位覆盖情况
    coverage_df = pd.read_excel(result_file, sheet_name='部位覆盖情况')
    print(f"\n部位覆盖情况统计:")
    print(coverage_df['是否有DR项目'].value_counts())

    # 检查摆位编码
    position_codes = df[df['摆位'] != '']['摆位']
    dr_codes_with_pos = df[df['DR检查项目编码'] != '']['DR检查项目编码']

    print(f"\n摆位编码检查:")
    print(f"有摆位信息的记录: {len(position_codes)}")
    print(f"有DR检查项目编码的记录: {len(dr_codes_with_pos)}")

    # 显示一些具体的匹配示例
    print(f"\n匹配示例:")
    sample_records = df[df['项目名称'] != ''].head(5)
    for idx, row in sample_records.iterrows():
        print(f"部位: {row['三级部位']} | 项目: {row['项目名称']} | 摆位: {row['摆位']} | 编码: {row['DR检查项目编码']}")

except Exception as e:
    print(f"读取文件时出错: {e}")
    import traceback
    traceback.print_exc()
