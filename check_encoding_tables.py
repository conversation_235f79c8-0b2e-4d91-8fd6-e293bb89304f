import pandas as pd
import os
from datetime import datetime
import re

# 读取Excel文件
file_path = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0706.xlsx'

try:
    # 读取所有sheet
    excel_data = pd.read_excel(file_path, sheet_name=None)
    
    print("Available sheets:", list(excel_data.keys()))
    
    # 检查DR sheet和三级部位 sheet
    if 'DR' in excel_data and '三级部位' in excel_data:
        dr_sheet = excel_data['DR'].copy()
        level3_sheet = excel_data['三级部位'].copy()
        
        print(f"\nDR sheet形状: {dr_sheet.shape}")
        print(f"三级部位 sheet形状: {level3_sheet.shape}")
        
        # 数据清理函数
        def clean_text(text):
            if pd.isna(text):
                return ''
            text = str(text)
            # 去除所有空格（包括全角空格）
            text = re.sub(r'\s+', '', text)
            # 去除特殊字符
            text = text.strip()
            return text
        
        # 清理DR sheet的三级部位列
        dr_sheet['三级部位_清理'] = dr_sheet['三级部位'].apply(clean_text)
        # 过滤掉空值
        dr_sheet = dr_sheet[dr_sheet['三级部位_清理'] != '']
        dr_sheet = dr_sheet[dr_sheet['三级部位_清理'] != 'nan']
        
        # 清理三级部位 sheet的三级部位列
        level3_sheet['三级部位_清理'] = level3_sheet['三级部位'].apply(clean_text)
        # 过滤掉空值
        level3_sheet = level3_sheet[level3_sheet['三级部位_清理'] != '']
        level3_sheet = level3_sheet[level3_sheet['三级部位_清理'] != 'nan']
        
        print(f"\n清理后DR sheet形状: {dr_sheet.shape}")
        print(f"清理后三级部位 sheet形状: {level3_sheet.shape}")
        
        # 检查匹配情况
        dr_unique = set(dr_sheet['三级部位_清理'].unique())
        level3_unique = set(level3_sheet['三级部位_清理'].unique())
        
        matched_parts = dr_unique.intersection(level3_unique)
        unmatched_parts = dr_unique - level3_unique
        
        print(f"\nDR中的三级部位数量: {len(dr_unique)}")
        print(f"三级部位 sheet中的三级部位数量: {len(level3_unique)}")
        print(f"可以精确匹配的三级部位数量: {len(matched_parts)}")
        print(f"无法匹配的三级部位数量: {len(unmatched_parts)}")
        
        print(f"\n可精确匹配的三级部位示例 (前10个): {list(matched_parts)[:10]}")
        print(f"\n无法匹配的三级部位示例 (前10个): {list(unmatched_parts)[:10]}")
        
        # 创建映射字典来处理相似匹配
        mapping_dict = {}
        
        for dr_part in dr_unique:
            # 首先尝试精确匹配
            if dr_part in level3_unique:
                mapping_dict[dr_part] = dr_part
            else:
                # 尝试模糊匹配
                found = False
                
                # 1. 检查是否DR部位包含在三级部位sheet的某个部位中
                for l3_part in level3_unique:
                    if dr_part in l3_part:
                        mapping_dict[dr_part] = l3_part
                        found = True
                        break
                
                # 2. 检查是否三级部位sheet的某个部位包含在DR部位中
                if not found:
                    for l3_part in level3_unique:
                        if l3_part in dr_part:
                            mapping_dict[dr_part] = l3_part
                            found = True
                            break
                
                # 3. 特殊情况处理
                if not found:
                    special_mappings = {
                        '眼眶': '左眼眶',
                        '右股骨髁间窝': '右股骨',
                        '左股骨髁间窝': '左股骨',
                    }
                    if dr_part in special_mappings:
                        if special_mappings[dr_part] in level3_unique:
                            mapping_dict[dr_part] = special_mappings[dr_part]
        
        print(f"\n创建的映射关系数量: {len(mapping_dict)}")
        exact_mappings = [(k, v) for k, v in mapping_dict.items() if k == v]
        fuzzy_mappings = [(k, v) for k, v in mapping_dict.items() if k != v]
        print(f"精确映射数量: {len(exact_mappings)}")
        print(f"模糊映射数量: {len(fuzzy_mappings)}")
        
        if fuzzy_mappings:
            print(f"\n模糊映射示例 (前10个):")
            for dr_part, l3_part in fuzzy_mappings[:10]:
                print(f"'{dr_part}' -> '{l3_part}'")
        
        # 基于映射进行合并
        # 首先为DR sheet添加映射列
        dr_sheet['三级部位_映射'] = dr_sheet['三级部位_清理'].map(mapping_dict)
        
        # 只保留有映射的行
        dr_mapped = dr_sheet[dr_sheet['三级部位_映射'].notna()].copy()
        
        print(f"\n有映射的DR数据: {dr_mapped.shape[0]} 行")
        
        # 基于映射后的三级部位进行左连接
        merged_data = pd.merge(dr_mapped, level3_sheet, left_on='三级部位_映射', right_on='三级部位_清理', how='left', suffixes=('_DR', '_三级部位'))
        
        print(f"合并后数据形状: {merged_data.shape}")
        
        # 选择需要的列，优先使用三级部位sheet的数据
        result_data = []
        
        for index, row in merged_data.iterrows():
            # 检查是否有匹配的数据（三级部位sheet的数据不为空）
            has_match = pd.notna(row.get('一级编码_三级部位'))
            
            result_row = {
                '一级编码': row.get('一级编码_三级部位') if has_match else None,
                '一级部位': row.get('一级部位_三级部位') if has_match else None,
                '二级编码': row.get('二级编码_三级部位') if has_match else None,
                '二级部位': row.get('二级部位_三级部位') if has_match else None,
                '三级编码': row.get('三级编码_三级部位') if has_match else None,
                '三级部位': row.get('三级部位_DR')  # 使用DR中的原始三级部位
            }
            result_data.append(result_row)
        
        # 创建结果DataFrame
        final_result = pd.DataFrame(result_data)
        
        # 统计匹配情况
        matched_count = final_result[final_result[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].notna().any(axis=1)].shape[0]
        unmatched_count = final_result[final_result[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].isna().all(axis=1)].shape[0]
        
        print(f"\n匹配统计:")
        print(f"成功匹配: {matched_count} 行")
        print(f"未匹配: {unmatched_count} 行")
        print(f"总计: {final_result.shape[0]} 行")
        
        # 处理未映射的DR数据
        dr_unmapped = dr_sheet[dr_sheet['三级部位_映射'].isna()].copy()
        
        if not dr_unmapped.empty:
            print(f"\n未映射的DR数据: {dr_unmapped.shape[0]} 行")
            
            # 为未映射的数据创建空行
            unmapped_result = []
            for index, row in dr_unmapped.iterrows():
                result_row = {
                    '一级编码': None,
                    '一级部位': None,
                    '二级编码': None,
                    '二级部位': None,
                    '三级编码': None,
                    '三级部位': row.get('三级部位')  # 使用原始三级部位
                }
                unmapped_result.append(result_row)
            
            unmapped_df = pd.DataFrame(unmapped_result)
            
            # 合并匹配和未匹配的数据
            if not final_result.empty:
                # 添加空行分隔
                empty_row = {col: None for col in final_result.columns}
                final_output = final_result.to_dict('records') + [empty_row] + unmapped_df.to_dict('records')
            else:
                final_output = unmapped_df.to_dict('records')
            
            final_df = pd.DataFrame(final_output)
        else:
            final_df = final_result
        
        # 保存到新的Excel文件
        output_dir = '/Users/<USER>/Desktop/12-new/output'
        os.makedirs(output_dir, exist_ok=True)
        output_file = f'{output_dir}/DR三级部位匹配结果_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        final_df.to_excel(output_file, index=False, sheet_name='匹配结果')
        
        print(f"\n结果已保存到: {output_file}")
        print(f"\n文件包含:")
        print(f"- 匹配成功的数据: {matched_count} 行")
        if not dr_unmapped.empty:
            print(f"- 空行分隔: 1 行")
            print(f"- 未匹配的数据: {dr_unmapped.shape[0]} 行")
        print(f"- 总计: {final_df.shape[0]} 行")
        
        if matched_count > 0:
            print(f"\n匹配成功的数据示例 (前5行):")
            matched_data = final_result[final_result[['一级编码', '一级部位', '二级编码', '二级部位', '三级编码']].notna().any(axis=1)]
            print(matched_data.head())
        
        if not dr_unmapped.empty:
            print(f"\n未匹配数据的三级部位列表 (前20个):")
            print(dr_unmapped['三级部位'].head(20).tolist())
        
except Exception as e:
    print(f"Error reading Excel file: {e}")
    import traceback
    traceback.print_exc()