import pandas as pd
import re

# 读取Excel文件
file_path = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0706.xlsx'

try:
    excel_data = pd.read_excel(file_path, sheet_name=None)
    
    dr_sheet = excel_data['DR'].copy()
    level3_sheet = excel_data['三级部位'].copy()
    
    print("=== 最终调试分析 ===")
    
    # 数据清理函数
    def clean_text(text):
        if pd.isna(text):
            return ''
        text = str(text)
        text = re.sub(r'\s+', '', text)
        text = text.strip()
        return text
    
    # 清理数据
    dr_sheet['三级部位_清理'] = dr_sheet['三级部位'].apply(clean_text)
    level3_sheet['三级部位_清理'] = level3_sheet['三级部位'].apply(clean_text)
    
    # 过滤空值
    dr_clean = dr_sheet[dr_sheet['三级部位_清理'] != ''][dr_sheet['三级部位_清理'] != 'nan']
    level3_clean = level3_sheet[level3_sheet['三级部位_清理'] != ''][level3_sheet['三级部位_清理'] != 'nan']
    
    print(f"\nDR sheet清理后: {dr_clean.shape[0]} 行")
    print(f"三级部位 sheet清理后: {level3_clean.shape[0]} 行")
    
    # 获取唯一值
    dr_unique = sorted(dr_clean['三级部位_清理'].unique())
    level3_unique = sorted(level3_clean['三级部位_清理'].unique())
    
    print(f"\nDR中唯一三级部位: {len(dr_unique)} 个")
    print(f"三级部位sheet中唯一三级部位: {len(level3_unique)} 个")
    
    print(f"\nDR中的前20个三级部位:")
    for i, part in enumerate(dr_unique[:20]):
        print(f"{i+1:2d}. '{part}' (长度: {len(part)})")
    
    print(f"\n三级部位sheet中的前20个三级部位:")
    for i, part in enumerate(level3_unique[:20]):
        print(f"{i+1:2d}. '{part}' (长度: {len(part)})")
    
    # 检查精确匹配
    exact_matches = set(dr_unique).intersection(set(level3_unique))
    print(f"\n精确匹配数量: {len(exact_matches)}")
    if exact_matches:
        print(f"精确匹配的部位: {list(exact_matches)[:10]}")
    
    # 检查字符编码问题
    print(f"\n检查字符编码:")
    for part in dr_unique[:5]:
        print(f"DR: '{part}' -> bytes: {part.encode('utf-8')}")
    
    for part in level3_unique[:5]:
        print(f"三级部位: '{part}' -> bytes: {part.encode('utf-8')}")
    
    # 检查是否有相似的部位
    print(f"\n检查相似匹配:")
    similar_count = 0
    for dr_part in dr_unique[:20]:
        for l3_part in level3_unique:
            if dr_part in l3_part or l3_part in dr_part:
                print(f"相似: DR='{dr_part}' <-> 三级部位='{l3_part}'")
                similar_count += 1
                break
    
    print(f"\n找到 {similar_count} 个相似匹配")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()